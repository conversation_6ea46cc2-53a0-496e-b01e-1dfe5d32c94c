{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:203:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:35:13:3513"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:203:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:36:00:360"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:203:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:37:15:3715"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:204:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:48:40:4840"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:204:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:48:40:4840"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:205:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:48:54:4854"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:205:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:48:54:4854"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:49:08:498"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 04:49:08:498"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3069","port":3069,"stack":"Error: listen EADDRINUSE: address already in use :::3069\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:00:34:034"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:01:55:155"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:02:11:211"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:02:24:224"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:02:34:234"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:02:55:255"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:206:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:04:42:442"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:209:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:13:59:1359"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:25:09:259"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:25:13:2513"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:25:43:2543"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:26:08:268"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:26:19:2619"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:26:31:2631"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:26:41:2641"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:26:51:2651"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:28:06:286"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:28:19:2819"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:215:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-05 05:32:59:3259"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:20.840Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"0b3db91d-d103-4fb2-b954-404f1bb5f406","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:20.840Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921","userId":"cosmic-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:20.842Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"f289c2e5-6b97-448a-aa79-d73ed93131f1","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:20.842Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921","userId":"cosmic-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:20.838Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"3bc4713f-d364-4827-bfa6-1ebebf169834","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:20.838Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921","userId":"cosmic-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:21.738Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"2118b1e7-11c6-4894-aa36-c3a711cbd931","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:21.738Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922","userId":"cosmic-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:21.753Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"9f4926a4-de5b-4be8-aff4-439c4ea746e8","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:21.753Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922","userId":"cosmic-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:21.764Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"3a661dea-d9af-4707-b2c6-99ee6d317f8f","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:21.764Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922","userId":"cosmic-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:24.851Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"f50caeef-e702-4a41-8973-cd345e7c0e33","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:24.851Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:24:4924"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:24:4924","userId":"cosmic-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.592Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"92debb8a-5b82-46ab-b570-80752273facc","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.592Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.594Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"dd1cf63e-a39e-4403-b6c7-e962e0a99183","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.594Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.597Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"38496b3b-9a09-4442-8a54-93766972cf1c","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.597Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.618Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"6f0284df-c65e-4500-844c-b86d330287ae","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.618Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.624Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"768e280b-dd88-4440-94d3-a6c1d5811ea5","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.624Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.634Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"684e4c78-868c-46bd-8e0d-d8a0d27109b4","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.634Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.657Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"62a6d995-18c4-4fe3-9fc9-aaa517029427","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.657Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.663Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"31cc490c-1be8-4b19-8801-7df5fbd793a5","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.663Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.685Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"2280ae95-6744-48e6-b679-d489f119fdfd","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.685Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.713Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"8d82b8b0-ee0d-4492-89ea-0a3095be85bd","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.713Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:29.695Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"089a3279-7e0d-40bf-bdd4-0cc7320b10ad","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:29.695Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:29:4929"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:29:4929","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:29.741Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"5f9b2047-36d7-4e08-8ad6-b9adba41e739","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:29.741Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:29:4929"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:29:4929","userId":"research-demo-user"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:34.611Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"d57767e3-25f0-4da8-bc3a-4c2eefc5aab4","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:34.611Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:34:4934"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:34:4934","userId":"research-demo-user"}
