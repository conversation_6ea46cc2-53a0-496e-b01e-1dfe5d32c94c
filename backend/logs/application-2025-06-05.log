{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:00:12:012"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:00:12:012"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:00:15:015"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:00:15:015"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:00:15:015"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:00:15:015"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:00:15:015"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:00:15:015"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:00:15:015"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:00:15:015"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:00:15:015"}
{"contentLength":"1929","duration":2979,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (2979ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:01:09:19","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":13,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (13ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:01:09:19","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"duration":3925,"level":"info","message":"Graph: Analyze supplement stack (3925ms)","nodeCount":0,"operation":"Analyze supplement stack","relationshipCount":0,"timestamp":"2025-06-05 04:01:10:110"}
{"contentLength":"214","duration":3933,"ip":"::ffff:127.0.0.1","level":"info","message":"API: POST /api/graph/analyze-stack - 200 (3933ms)","method":"POST","statusCode":200,"timestamp":"2025-06-05 04:01:10:110","url":"/api/graph/analyze-stack","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:12:43:1243"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:12:58:1258"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:13:11:1311"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:13:13:1313"}
{"contentLength":"1929","duration":15,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (15ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:13:15:1315","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"214","duration":11,"ip":"::ffff:127.0.0.1","level":"info","message":"API: POST /api/graph/analyze-stack - 200 (11ms)","method":"POST","statusCode":200,"timestamp":"2025-06-05 04:13:15:1315","url":"/api/graph/analyze-stack","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":11,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (11ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:13:15:1315","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:13:32:1332","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":9,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (9ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:13:32:1332","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":11,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (11ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:13:57:1357","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":10,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (10ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:14:14:1414","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":32,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (32ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:14:14:1414","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":9,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (9ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:15:59:1559","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"214","duration":12,"ip":"::ffff:127.0.0.1","level":"info","message":"API: POST /api/graph/analyze-stack - 200 (12ms)","method":"POST","statusCode":200,"timestamp":"2025-06-05 04:15:59:1559","url":"/api/graph/analyze-stack","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":24,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (24ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:15:59:1559","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":10,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (10ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:16:34:1634","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":9,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (9ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:16:34:1634","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":26,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (26ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:16:34:1634","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":33,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (33ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:16:34:1634","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:16:34:1634","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":7,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (7ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:16:34:1634","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"Research search completed","query":{"includeMetaAnalysis":true,"maxResults":20,"minEvidenceLevel":4,"supplement":"tyrozyna"},"resultsCount":0,"timestamp":"2025-06-05 04:18:13:1813"}
{"insightsCount":0,"level":"info","message":"Research AI search completed","query":{"includeMetaAnalysis":true,"maxResults":20,"minEvidenceLevel":4,"supplement":"tyrozyna"},"resultsCount":0,"timestamp":"2025-06-05 04:18:13:1813","userId":"cosmic-user"}
{"contentLength":"309","duration":1013,"ip":"::ffff:127.0.0.1","level":"info","message":"API: POST /api/research-ai/search - 200 (1013ms)","method":"POST","statusCode":200,"timestamp":"2025-06-05 04:18:13:1813","url":"/api/research-ai/search","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":6,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (6ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:18:20:1820","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":6,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (6ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:18:20:1820","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"214","duration":9,"ip":"::ffff:127.0.0.1","level":"info","message":"API: POST /api/graph/analyze-stack - 200 (9ms)","method":"POST","statusCode":200,"timestamp":"2025-06-05 04:18:20:1820","url":"/api/graph/analyze-stack","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":7,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (7ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:22:12:2212","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"214","duration":9,"ip":"::ffff:127.0.0.1","level":"info","message":"API: POST /api/graph/analyze-stack - 200 (9ms)","method":"POST","statusCode":200,"timestamp":"2025-06-05 04:22:12:2212","url":"/api/graph/analyze-stack","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:22:12:2212","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":7,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (7ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:23:50:2350","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:23:50:2350","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"214","duration":8,"ip":"::ffff:127.0.0.1","level":"info","message":"API: POST /api/graph/analyze-stack - 200 (8ms)","method":"POST","statusCode":200,"timestamp":"2025-06-05 04:23:50:2350","url":"/api/graph/analyze-stack","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":7,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (7ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:23:57:2357","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":6,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (6ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:23:57:2357","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":9,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (9ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:23:57:2357","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":9,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (9ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:23:57:2357","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:23:57:2357","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":11,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (11ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:23:57:2357","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:26:50:2650"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:37:12:3712"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:45:05:455"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:48:37:4837"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:48:52:4852"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:49:06:496"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:50:09:509"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"📴 Received SIGINT. Starting graceful shutdown...","timestamp":"2025-06-05 04:55:37:5537"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:57:01:571"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 04:57:09:579"}
{"contentLength":"1929","duration":26,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (26ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:57:20:5720","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":29,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (29ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:57:20:5720","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":40,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (40ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:57:20:5720","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":19,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (19ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:57:20:5720","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":12,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (12ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:57:20:5720","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":23,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (23ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:57:20:5720","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":13,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (13ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:00:13:013","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:00:33:033"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:00:33:033"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:00:33:033"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:00:33:033"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:00:47:047"}
{"contentLength":"1929","duration":10,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (10ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:01:17:117","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":9,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (9ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:01:17:117","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":10,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (10ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:01:17:117","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":11,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (11ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:01:17:117","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":12,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (12ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:01:17:117","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":16,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (16ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:01:17:117","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:01:52:152"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:01:52:152"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:01:54:154"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:01:54:154"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:01:54:154"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:01:54:154"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:02:08:28"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:02:08:28"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:02:21:221"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:02:21:221"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:02:31:231"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:02:31:231"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:02:51:251"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:02:51:251"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:02:55:255"}
{"level":"warn","message":"Profile for user cosmic-user not found during GET request.","timestamp":"2025-06-05 05:03:31:331"}
{"contentLength":"1929","duration":27,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (27ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:31:331","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":20,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (20ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:31:331","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"warn","message":"Profile for user cosmic-user not found during GET request.","timestamp":"2025-06-05 05:03:31:331"}
{"contentLength":"1929","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:31:331","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":43,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (43ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:31:331","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:31:331","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":6,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (6ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:31:331","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"warn","message":"Profile for user cosmic-user not found during GET request.","timestamp":"2025-06-05 05:03:37:337"}
{"contentLength":"1929","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:37:337","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:37:337","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":9,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (9ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:37:337","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":7,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (7ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:37:337","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"warn","message":"Profile for user cosmic-user not found during GET request.","timestamp":"2025-06-05 05:03:37:337"}
{"contentLength":"1929","duration":9,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (9ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:37:337","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":11,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (11ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:37:337","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:04:39:439"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:04:39:439"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:04:42:442"}
