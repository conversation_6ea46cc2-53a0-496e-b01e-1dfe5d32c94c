{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:00:12:012"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:00:12:012"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:00:14:014"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:00:15:015"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:00:15:015"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:00:15:015"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:00:15:015"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:00:15:015"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:00:15:015"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:00:15:015"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:00:15:015"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:00:15:015"}
{"contentLength":"1929","duration":2979,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (2979ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:01:09:19","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":13,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (13ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:01:09:19","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"duration":3925,"level":"info","message":"Graph: Analyze supplement stack (3925ms)","nodeCount":0,"operation":"Analyze supplement stack","relationshipCount":0,"timestamp":"2025-06-05 04:01:10:110"}
{"contentLength":"214","duration":3933,"ip":"::ffff:127.0.0.1","level":"info","message":"API: POST /api/graph/analyze-stack - 200 (3933ms)","method":"POST","statusCode":200,"timestamp":"2025-06-05 04:01:10:110","url":"/api/graph/analyze-stack","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:12:43:1243"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:12:45:1245"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:12:58:1258"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:13:00:130"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:13:11:1311"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:13:13:1313"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:13:13:1313"}
{"contentLength":"1929","duration":15,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (15ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:13:15:1315","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"214","duration":11,"ip":"::ffff:127.0.0.1","level":"info","message":"API: POST /api/graph/analyze-stack - 200 (11ms)","method":"POST","statusCode":200,"timestamp":"2025-06-05 04:13:15:1315","url":"/api/graph/analyze-stack","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":11,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (11ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:13:15:1315","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:13:32:1332","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":9,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (9ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:13:32:1332","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":11,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (11ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:13:57:1357","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":10,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (10ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:14:14:1414","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":32,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (32ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:14:14:1414","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":9,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (9ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:15:59:1559","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"214","duration":12,"ip":"::ffff:127.0.0.1","level":"info","message":"API: POST /api/graph/analyze-stack - 200 (12ms)","method":"POST","statusCode":200,"timestamp":"2025-06-05 04:15:59:1559","url":"/api/graph/analyze-stack","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":24,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (24ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:15:59:1559","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":10,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (10ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:16:34:1634","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":9,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (9ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:16:34:1634","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":26,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (26ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:16:34:1634","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":33,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (33ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:16:34:1634","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:16:34:1634","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":7,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (7ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:16:34:1634","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"Research search completed","query":{"includeMetaAnalysis":true,"maxResults":20,"minEvidenceLevel":4,"supplement":"tyrozyna"},"resultsCount":0,"timestamp":"2025-06-05 04:18:13:1813"}
{"insightsCount":0,"level":"info","message":"Research AI search completed","query":{"includeMetaAnalysis":true,"maxResults":20,"minEvidenceLevel":4,"supplement":"tyrozyna"},"resultsCount":0,"timestamp":"2025-06-05 04:18:13:1813","userId":"cosmic-user"}
{"contentLength":"309","duration":1013,"ip":"::ffff:127.0.0.1","level":"info","message":"API: POST /api/research-ai/search - 200 (1013ms)","method":"POST","statusCode":200,"timestamp":"2025-06-05 04:18:13:1813","url":"/api/research-ai/search","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":6,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (6ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:18:20:1820","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":6,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (6ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:18:20:1820","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"214","duration":9,"ip":"::ffff:127.0.0.1","level":"info","message":"API: POST /api/graph/analyze-stack - 200 (9ms)","method":"POST","statusCode":200,"timestamp":"2025-06-05 04:18:20:1820","url":"/api/graph/analyze-stack","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":7,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (7ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:22:12:2212","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"214","duration":9,"ip":"::ffff:127.0.0.1","level":"info","message":"API: POST /api/graph/analyze-stack - 200 (9ms)","method":"POST","statusCode":200,"timestamp":"2025-06-05 04:22:12:2212","url":"/api/graph/analyze-stack","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:22:12:2212","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":7,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (7ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:23:50:2350","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:23:50:2350","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"214","duration":8,"ip":"::ffff:127.0.0.1","level":"info","message":"API: POST /api/graph/analyze-stack - 200 (8ms)","method":"POST","statusCode":200,"timestamp":"2025-06-05 04:23:50:2350","url":"/api/graph/analyze-stack","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":7,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (7ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:23:57:2357","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":6,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (6ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:23:57:2357","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":9,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (9ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:23:57:2357","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":9,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (9ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:23:57:2357","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:23:57:2357","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":11,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (11ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:23:57:2357","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:26:50:2650"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:26:52:2652"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:35:13:3513"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:36:00:360"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:37:12:3712"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:37:15:3715"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:45:05:455"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:45:33:4533"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:48:37:4837"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:48:39:4839"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:48:40:4840"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:48:52:4852"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:48:53:4853"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:48:54:4854"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:49:06:496"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:49:08:498"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:50:09:509"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:51:20:5120"}
{"level":"info","message":"📴 Received SIGINT. Starting graceful shutdown...","timestamp":"2025-06-05 04:55:37:5537"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 04:56:15:5615"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 04:57:01:571"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 04:57:09:579"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 04:57:09:579"}
{"contentLength":"1929","duration":26,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (26ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:57:20:5720","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":29,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (29ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:57:20:5720","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":40,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (40ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:57:20:5720","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":19,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (19ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:57:20:5720","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":12,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (12ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:57:20:5720","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":23,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (23ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 04:57:20:5720","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":13,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (13ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:00:13:013","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:00:33:033"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:00:33:033"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:00:33:033"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:00:33:033"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:00:34:034"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:00:47:047"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:00:47:047"}
{"contentLength":"1929","duration":10,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (10ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:01:17:117","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":9,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (9ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:01:17:117","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":10,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (10ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:01:17:117","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":11,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (11ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:01:17:117","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":12,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (12ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:01:17:117","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"1929","duration":16,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (16ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:01:17:117","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:01:52:152"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:01:52:152"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:01:54:154"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:01:54:154"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:01:54:154"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:01:54:154"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:01:55:155"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:02:08:28"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:02:08:28"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:02:11:211"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:02:21:221"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:02:21:221"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:02:24:224"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:02:31:231"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:02:31:231"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:02:34:234"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:02:51:251"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:02:51:251"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:02:54:254"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:02:55:255"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:02:55:255"}
{"level":"warn","message":"Profile for user cosmic-user not found during GET request.","timestamp":"2025-06-05 05:03:31:331"}
{"contentLength":"1929","duration":27,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (27ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:31:331","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":20,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (20ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:31:331","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"warn","message":"Profile for user cosmic-user not found during GET request.","timestamp":"2025-06-05 05:03:31:331"}
{"contentLength":"1929","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:31:331","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":43,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (43ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:31:331","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:31:331","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":6,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (6ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:31:331","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"warn","message":"Profile for user cosmic-user not found during GET request.","timestamp":"2025-06-05 05:03:37:337"}
{"contentLength":"1929","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:37:337","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":8,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (8ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:37:337","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":9,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (9ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:37:337","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":7,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 404 (7ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:37:337","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"warn","message":"Profile for user cosmic-user not found during GET request.","timestamp":"2025-06-05 05:03:37:337"}
{"contentLength":"1929","duration":9,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 404 (9ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:37:337","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"contentLength":"52","duration":11,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 404 (11ms)","method":"GET","statusCode":404,"timestamp":"2025-06-05 05:03:37:337","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:04:39:439"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:04:39:439"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:04:42:442"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:13:57:1357"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:13:57:1357"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:13:59:1359"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:14:09:149"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:14:09:149"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:21:40:2140"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:22:57:2257"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:22:57:2257"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:22:59:2259"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:23:23:2323"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:23:23:2323"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:23:25:2325"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:24:14:2414"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:24:14:2414"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:24:16:2416"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:24:16:2416"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:24:16:2416"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:24:16:2416"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:24:16:2416"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:24:16:2416"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:24:17:2417"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:24:27:2427"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:24:27:2427"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:24:29:2429"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:24:40:2440"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:24:40:2440"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"🚀 Server running on port 3069","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"🔗 API URL: http://localhost:3069/api","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3069/agui/ws","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"💚 Health check: http://localhost:3069/health","timestamp":"2025-06-05 05:24:42:2442"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:24:58:2458"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:25:00:250"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:25:09:259"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:25:11:2511"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:25:13:2513"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:25:31:2531"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:25:42:2542"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:25:42:2542"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:25:42:2542"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:25:42:2542"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:25:42:2542"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:25:42:2542"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:25:42:2542"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:25:42:2542"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:25:42:2542"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:25:42:2542"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:25:42:2542"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:25:42:2542"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:25:42:2542"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:25:42:2542"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:25:43:2543"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:26:05:265"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:26:07:267"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:26:07:267"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:07:267"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:07:267"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:26:07:267"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:26:07:267"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:26:07:267"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:26:07:267"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:26:07:267"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:07:267"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:07:267"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:26:07:267"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:26:07:267"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:26:07:267"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:26:07:267"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:26:07:267"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:26:08:268"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:26:16:2616"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:26:18:2618"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:26:18:2618"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:18:2618"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:18:2618"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:26:18:2618"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:26:18:2618"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:26:18:2618"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:26:18:2618"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:26:18:2618"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:26:18:2618"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:18:2618"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:18:2618"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:26:18:2618"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:26:18:2618"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:26:19:2619"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:26:28:2628"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:26:30:2630"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:26:31:2631"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:26:31:2631"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:26:31:2631"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:26:31:2631"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:26:31:2631"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:26:31:2631"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:26:31:2631"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:26:31:2631"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:26:31:2631"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:26:31:2631"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:26:31:2631"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:26:31:2631"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:26:31:2631"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:26:31:2631"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:26:31:2631"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:26:37:2637"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:26:41:2641"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:26:48:2648"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:26:50:2650"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:26:51:2651"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:28:03:283"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:28:06:286"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:28:16:2816"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:28:18:2818"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-05 05:32:54:3254"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:32:57:3257"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:32:57:3257"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:32:57:3257"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:32:57:3257"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:32:57:3257"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:32:57:3257"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:32:57:3257"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:32:57:3257"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:32:57:3257"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:32:57:3257"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:32:57:3257"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:32:57:3257"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:32:58:3258"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:32:58:3258"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:32:58:3258"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:32:58:3258"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"Initializing CrewAI Integration Service...","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"CrewAI Integration Service initialized successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"MCP Research Orchestrator initialized with 6 tools","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"🔌 Graph WebSocket service initialized","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-05 05:32:59:3259"}
{"level":"warn","message":"User profile cosmic-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:20:4920"}
{"level":"warn","message":"User profile cosmic-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:20:4920"}
{"level":"warn","message":"User profile cosmic-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:20:4920"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:20.840Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"0b3db91d-d103-4fb2-b954-404f1bb5f406","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:20.840Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921","userId":"cosmic-user"}
{"contentLength":"1911","duration":776,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 500 (776ms)","method":"GET","statusCode":500,"timestamp":"2025-06-05 05:49:21:4921","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:20.842Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"f289c2e5-6b97-448a-aa79-d73ed93131f1","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:20.842Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921","userId":"cosmic-user"}
{"contentLength":"1923","duration":772,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 500 (772ms)","method":"GET","statusCode":500,"timestamp":"2025-06-05 05:49:21:4921","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:20.838Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"3bc4713f-d364-4827-bfa6-1ebebf169834","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:20.838Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:21:4921","userId":"cosmic-user"}
{"contentLength":"1933","duration":766,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 500 (766ms)","method":"GET","statusCode":500,"timestamp":"2025-06-05 05:49:21:4921","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"warn","message":"User profile cosmic-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:21:4921"}
{"level":"warn","message":"User profile cosmic-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:21:4921"}
{"level":"warn","message":"User profile cosmic-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:21:4921"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:21.738Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"2118b1e7-11c6-4894-aa36-c3a711cbd931","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:21.738Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922","userId":"cosmic-user"}
{"contentLength":"1933","duration":492,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/cosmic-user?limit=20 - 500 (492ms)","method":"GET","statusCode":500,"timestamp":"2025-06-05 05:49:22:4922","url":"/api/research-ai/insights/cosmic-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:21.753Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"9f4926a4-de5b-4be8-aff4-439c4ea746e8","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:21.753Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922","userId":"cosmic-user"}
{"contentLength":"1911","duration":499,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/cosmic-user - 500 (499ms)","method":"GET","statusCode":500,"timestamp":"2025-06-05 05:49:22:4922","url":"/api/profile/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:21.764Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"3a661dea-d9af-4707-b2c6-99ee6d317f8f","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:21.764Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:22:4922","userId":"cosmic-user"}
{"contentLength":"1923","duration":505,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/cosmic-user - 500 (505ms)","method":"GET","statusCode":500,"timestamp":"2025-06-05 05:49:22:4922","url":"/api/research-ai/monitor/cosmic-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"Research search completed","query":{"includeMetaAnalysis":true,"maxResults":20,"minEvidenceLevel":4,"supplement":"tyrozyna"},"resultsCount":0,"timestamp":"2025-06-05 05:49:24:4924"}
{"level":"warn","message":"User profile cosmic-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:24:4924"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:24.851Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"f50caeef-e702-4a41-8973-cd345e7c0e33","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:24.851Z","userId":"cosmic-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:24:4924"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:24:4924","userId":"cosmic-user"}
{"contentLength":"1911","duration":1014,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: POST /api/research-ai/search - 500 (1014ms)","method":"POST","statusCode":500,"timestamp":"2025-06-05 05:49:24:4924","url":"/api/research-ai/search","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"warn","message":"User profile research-demo-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:28:4928"}
{"level":"warn","message":"User profile research-demo-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:28:4928"}
{"level":"warn","message":"User profile research-demo-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.592Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"92debb8a-5b82-46ab-b570-80752273facc","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.592Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"contentLength":"1918","duration":13,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/research-demo-user - 500 (13ms)","method":"GET","statusCode":500,"timestamp":"2025-06-05 05:49:28:4928","url":"/api/profile/research-demo-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.594Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"dd1cf63e-a39e-4403-b6c7-e962e0a99183","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.594Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"contentLength":"1930","duration":15,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/research-demo-user - 500 (15ms)","method":"GET","statusCode":500,"timestamp":"2025-06-05 05:49:28:4928","url":"/api/research-ai/monitor/research-demo-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.597Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"38496b3b-9a09-4442-8a54-93766972cf1c","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.597Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"contentLength":"1940","duration":18,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/research-demo-user?limit=20 - 500 (18ms)","method":"GET","statusCode":500,"timestamp":"2025-06-05 05:49:28:4928","url":"/api/research-ai/insights/research-demo-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"warn","message":"User profile research-demo-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:28:4928"}
{"level":"warn","message":"User profile research-demo-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.618Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"6f0284df-c65e-4500-844c-b86d330287ae","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.618Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"contentLength":"1918","duration":18,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/profile/research-demo-user - 500 (18ms)","method":"GET","statusCode":500,"timestamp":"2025-06-05 05:49:28:4928","url":"/api/profile/research-demo-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"warn","message":"User profile research-demo-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.624Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"768e280b-dd88-4440-94d3-a6c1d5811ea5","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.624Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"contentLength":"1930","duration":20,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/research-demo-user - 500 (20ms)","method":"GET","statusCode":500,"timestamp":"2025-06-05 05:49:28:4928","url":"/api/research-ai/monitor/research-demo-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.634Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"684e4c78-868c-46bd-8e0d-d8a0d27109b4","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.634Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"contentLength":"1940","duration":25,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/research-demo-user?limit=20 - 500 (25ms)","method":"GET","statusCode":500,"timestamp":"2025-06-05 05:49:28:4928","url":"/api/research-ai/insights/research-demo-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"warn","message":"User profile research-demo-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:28:4928"}
{"level":"warn","message":"User profile research-demo-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.657Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"62a6d995-18c4-4fe3-9fc9-aaa517029427","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.657Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"contentLength":"1930","duration":18,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/research-demo-user - 500 (18ms)","method":"GET","statusCode":500,"timestamp":"2025-06-05 05:49:28:4928","url":"/api/research-ai/monitor/research-demo-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.663Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"31cc490c-1be8-4b19-8801-7df5fbd793a5","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.663Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"contentLength":"1940","duration":16,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/research-demo-user?limit=20 - 500 (16ms)","method":"GET","statusCode":500,"timestamp":"2025-06-05 05:49:28:4928","url":"/api/research-ai/insights/research-demo-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"warn","message":"User profile research-demo-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.685Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"2280ae95-6744-48e6-b679-d489f119fdfd","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.685Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"contentLength":"1930","duration":16,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/monitor/research-demo-user - 500 (16ms)","method":"GET","statusCode":500,"timestamp":"2025-06-05 05:49:28:4928","url":"/api/research-ai/monitor/research-demo-user","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"warn","message":"User profile research-demo-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:28.713Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"8d82b8b0-ee0d-4492-89ea-0a3095be85bd","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:28.713Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:28:4928","userId":"research-demo-user"}
{"contentLength":"1940","duration":14,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: GET /api/research-ai/insights/research-demo-user?limit=20 - 500 (14ms)","method":"GET","statusCode":500,"timestamp":"2025-06-05 05:49:28:4928","url":"/api/research-ai/insights/research-demo-user?limit=20","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"Research search completed","query":{"includeMetaAnalysis":true,"maxResults":10,"minEvidenceLevel":4,"supplement":"Vitamin D3"},"resultsCount":1,"timestamp":"2025-06-05 05:49:29:4929"}
{"level":"warn","message":"User profile research-demo-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:29:4929"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:29.695Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"089a3279-7e0d-40bf-bdd4-0cc7320b10ad","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:29.695Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:29:4929"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:29:4929","userId":"research-demo-user"}
{"contentLength":"1911","duration":1013,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: POST /api/research-ai/search - 500 (1013ms)","method":"POST","statusCode":500,"timestamp":"2025-06-05 05:49:29:4929","url":"/api/research-ai/search","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"Research search completed","query":{"includeMetaAnalysis":true,"maxResults":10,"minEvidenceLevel":4,"supplement":"Vitamin D3"},"resultsCount":1,"timestamp":"2025-06-05 05:49:29:4929"}
{"level":"warn","message":"User profile research-demo-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:29:4929"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:29.741Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"5f9b2047-36d7-4e08-8ad6-b9adba41e739","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:29.741Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:29:4929"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:29:4929","userId":"research-demo-user"}
{"contentLength":"1911","duration":1015,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: POST /api/research-ai/search - 500 (1015ms)","method":"POST","statusCode":500,"timestamp":"2025-06-05 05:49:29:4929","url":"/api/research-ai/search","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"Research search completed","query":{"includeMetaAnalysis":true,"maxResults":20,"minEvidenceLevel":4,"supplement":"Tyrozyna"},"resultsCount":0,"timestamp":"2025-06-05 05:49:34:4934"}
{"level":"warn","message":"User profile research-demo-user not found. Creating a default profile.","timestamp":"2025-06-05 05:49:34:4934"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Neo4j query failed","parameters":{"profile":{"allergies":[],"createdAt":"2025-06-05T03:49:34.611Z","currentSupplements":[],"healthGoals":["General Wellness"],"id":"d57767e3-25f0-4da8-bc3a-4c2eefc5aab4","medicalConditions":[],"preferences":{"budget":100,"categories":["Vitamins","Minerals"],"glutenFree":false,"naturalOnly":true,"veganOnly":false},"profileData":{"activityLevel":"Moderate","age":30,"dietType":"Omnivore","gender":"Prefer not to say","weight":70},"restrictions":{"avoidIngredients":[],"maxDailyPills":5,"preferredBrands":[]},"updatedAt":"2025-06-05T03:49:34.611Z","userId":"research-demo-user"}},"query":"\n        CREATE (p:UserProfile $profile)\n        RETURN p\n      ","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:34:4934"}
{"error":"Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.","level":"error","message":"Failed to create user profile","stack":"Neo4jError: Property values can only be of primitive types or arrays thereof. Encountered: Map{glutenFree -> Boolean('false'), categories -> List{String(\"Vitamins\"), String(\"Minerals\")}, veganOnly -> Boolean('false'), naturalOnly -> Boolean('true'), budget -> Double(1.000000e+02)}.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:89:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async UserProfileService.createUserProfile (/home/<USER>/Suplementor/backend/src/services/UserProfileService.ts:106:7)","timestamp":"2025-06-05 05:49:34:4934","userId":"research-demo-user"}
{"contentLength":"1911","duration":1015,"ip":"::ffff:127.0.0.1","level":"warn","message":"API: POST /api/research-ai/search - 500 (1015ms)","method":"POST","statusCode":500,"timestamp":"2025-06-05 05:49:34:4934","url":"/api/research-ai/search","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
