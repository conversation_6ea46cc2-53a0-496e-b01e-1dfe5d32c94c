const OpenAI = require('openai');

// Test LM Studio connection
async function testLMStudioConnection() {
  console.log('🧪 Testing LM Studio connection...');
  
  const lmStudio = new OpenAI({
    apiKey: 'lm-studio', // LM Studio doesn't require a real API key
    baseURL: 'http://192.168.0.179:1234/v1',
  });

  try {
    // Test 1: List available models
    console.log('\n📋 Testing model listing...');
    const models = await lmStudio.models.list();
    console.log('✅ Available models:', models.data.map(m => m.id));

    // Test 2: Simple chat completion
    console.log('\n💬 Testing chat completion...');
    const completion = await lmStudio.chat.completions.create({
      model: 'gemma-3-4b-it-qat', // Use the specific model you mentioned
      messages: [
        {
          role: 'system',
          content: 'You are a helpful assistant specializing in supplements and health.'
        },
        {
          role: 'user',
          content: 'What are the main benefits of Vitamin D supplementation?'
        }
      ],
      temperature: 0.7,
      max_tokens: 500,
    });

    console.log('✅ Chat completion successful!');
    console.log('📝 Response:', completion.choices[0].message.content);

    // Test 3: Health-specific query
    console.log('\n🏥 Testing health-specific query...');
    const healthQuery = await lmStudio.chat.completions.create({
      model: 'gemma-3-4b-it-qat',
      messages: [
        {
          role: 'system',
          content: 'You are an AI research assistant specializing in supplement analysis. Provide evidence-based information with confidence scores.'
        },
        {
          role: 'user',
          content: 'Analyze the potential interactions between Vitamin D, Magnesium, and Omega-3 supplements. Include confidence scores for each interaction.'
        }
      ],
      temperature: 0.3,
      max_tokens: 800,
    });

    console.log('✅ Health query successful!');
    console.log('🔬 Analysis:', healthQuery.choices[0].message.content);

    // Test 4: JSON response formatting
    console.log('\n📊 Testing structured JSON response...');
    const jsonQuery = await lmStudio.chat.completions.create({
      model: 'gemma-3-4b-it-qat',
      messages: [
        {
          role: 'system',
          content: 'You are an AI that returns structured JSON responses for supplement analysis.'
        },
        {
          role: 'user',
          content: `Analyze Vitamin C supplementation and return a JSON object with this structure:
          {
            "supplement": "Vitamin C",
            "benefits": ["benefit1", "benefit2"],
            "risks": ["risk1", "risk2"],
            "interactions": [{"supplement": "name", "type": "synergistic/antagonistic", "severity": "low/medium/high"}],
            "confidence": 0.85,
            "evidence_level": "high/medium/low"
          }`
        }
      ],
      temperature: 0.2,
      max_tokens: 600,
    });

    console.log('✅ JSON response test successful!');
    console.log('📋 Structured data:', jsonQuery.choices[0].message.content);

    console.log('\n🎉 All LM Studio tests passed successfully!');
    console.log('🔗 LM Studio is ready for integration with the enhanced agentic system.');

  } catch (error) {
    console.error('❌ LM Studio connection failed:', error.message);
    console.error('🔧 Please check:');
    console.error('   1. LM Studio is running on http://192.168.0.179:1234');
    console.error('   2. The gemma-3-4b-it-qat model is loaded');
    console.error('   3. The server is accessible from this machine');
    console.error('   4. No firewall is blocking the connection');
  }
}

// Test connection with timeout
async function testWithTimeout() {
  const timeout = new Promise((_, reject) => 
    setTimeout(() => reject(new Error('Connection timeout after 30 seconds')), 30000)
  );

  try {
    await Promise.race([testLMStudioConnection(), timeout]);
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testWithTimeout();
