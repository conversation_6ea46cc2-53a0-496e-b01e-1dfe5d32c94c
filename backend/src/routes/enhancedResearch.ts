import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { AIService } from '@/services/AIService';
import ResearchAIService from '@/services/ResearchAIService';
import { MCPResearchOrchestrator } from '@/services/MCPResearchOrchestrator';
import { logger } from '@/utils/logger';
import { ValidationError, ExternalServiceError } from '@/middleware/errorHandler';

const router = express.Router();
const aiService = new AIService();
const researchAIService = new ResearchAIService();
const mcpOrchestrator = new MCPResearchOrchestrator();

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed: ' + errors.array().map(e => e.msg).join(', '));
  }
  next();
};

// POST /api/research/autonomous - Start autonomous research
router.post('/autonomous',
  body('supplement').isString().notEmpty(),
  body('goals').isArray().optional(),
  body('depth').isIn(['basic', 'comprehensive', 'exhaustive']).optional(),
  body('includeInteractions').isBoolean().optional(),
  body('includeClinicalTrials').isBoolean().optional(),
  body('includeUserReviews').isBoolean().optional(),
  body('userId').isString().optional(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const {
        supplement,
        goals = [],
        depth = 'comprehensive',
        includeInteractions = true,
        includeClinicalTrials = true,
        includeUserReviews = false,
        userId
      } = req.body;

      logger.info('🚀 Starting autonomous research', {
        supplement,
        depth,
        goals: goals.length,
        userId
      });

      // Create research flow with LM Studio integration
      const flowId = `flow-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // Start autonomous research using LM Studio
      const researchPromise = mcpOrchestrator.startAutonomousResearch({
        flowId,
        supplement,
        goals,
        depth,
        includeInteractions,
        includeClinicalTrials,
        includeUserReviews,
        userId,
        model: 'gemma-3-4b-it-qat' // Use the specific model from LM Studio
      });

      // Don't await - let it run in background
      researchPromise.catch(error => {
        logger.error('Autonomous research failed', error, { flowId, supplement });
      });

      res.json({
        success: true,
        flowId,
        message: 'Autonomous research started successfully',
        estimatedDuration: depth === 'basic' ? '2-3 minutes' : 
                          depth === 'comprehensive' ? '5-8 minutes' : '10-15 minutes'
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/research/:researchId/results - Get research results
router.get('/:researchId/results',
  param('researchId').isString().notEmpty(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const researchId = req.params['researchId'];
      
      const results = await mcpOrchestrator.getResearchResults(researchId);
      
      res.json({
        success: true,
        data: results,
        count: results.length
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/research/supplements/search - Enhanced supplement search
router.post('/supplements/search',
  body('query').isString().notEmpty(),
  body('includeTavily').isBoolean().optional(),
  body('includeBraveSearch').isBoolean().optional(),
  body('includeScientificDatabases').isBoolean().optional(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const {
        query,
        includeTavily = true,
        includeBraveSearch = true,
        includeScientificDatabases = true
      } = req.body;

      logger.info('🔍 Enhanced supplement search', { query, includeTavily, includeBraveSearch });

      // Use LM Studio for intelligent query processing
      const enhancedQuery = await aiService.chat(
        `Enhance this supplement search query for better research results: "${query}". 
         Return 3-5 specific search terms that would find the most relevant scientific information.`,
        { model: 'gemma-3-4b-it-qat', temperature: 0.3 }
      );

      // Perform multi-source search
      const searchResults = await researchAIService.enhancedSupplementSearch({
        originalQuery: query,
        enhancedQuery: enhancedQuery.message,
        includeTavily,
        includeBraveSearch,
        includeScientificDatabases
      });

      res.json({
        success: true,
        data: searchResults,
        query: {
          original: query,
          enhanced: enhancedQuery.message
        }
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/research/interactions - Analyze supplement interactions
router.post('/interactions',
  body('supplements').isArray().notEmpty(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const { supplements } = req.body;

      logger.info('⚡ Analyzing supplement interactions', { supplements });

      // Use LM Studio for advanced interaction analysis
      const interactionAnalysis = await aiService.checkInteractions(supplements, {
        includeFood: true,
        includeMedications: true
      });

      // Enhance with AI-powered risk assessment
      const riskAssessment = await aiService.chat(
        `Analyze the risk level and provide recommendations for these supplement interactions: ${JSON.stringify(interactionAnalysis)}. 
         Focus on practical safety advice and timing recommendations.`,
        { model: 'gemma-3-4b-it-qat', temperature: 0.2 }
      );

      res.json({
        success: true,
        data: {
          interactions: interactionAnalysis,
          riskAssessment: riskAssessment.message,
          supplements,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/research/insights - Get personalized insights
router.post('/insights',
  body('userId').isString().notEmpty(),
  body('supplements').isArray().optional(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const { userId, supplements = [] } = req.body;

      logger.info('🧠 Generating personalized insights', { userId, supplements: supplements.length });

      // Get user profile for personalization
      // This would typically fetch from UserProfileService
      const userContext = {
        supplements,
        userId,
        timestamp: new Date().toISOString()
      };

      // Generate personalized insights using LM Studio
      const insights = await aiService.chat(
        `Generate personalized health insights for a user taking these supplements: ${supplements.join(', ')}. 
         Include optimization recommendations, potential gaps, and timing suggestions. 
         Format as structured insights with priority levels.`,
        { model: 'gemma-3-4b-it-qat', temperature: 0.4 }
      );

      // Parse and structure the insights
      const structuredInsights = await aiService.analyzeText(insights.message, {
        analysisType: 'supplement',
        includeConfidence: true
      });

      res.json({
        success: true,
        data: structuredInsights,
        userContext,
        generatedAt: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/research/flows - List active research flows
router.get('/flows',
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const activeFlows = await mcpOrchestrator.getActiveFlows();
      
      res.json({
        success: true,
        data: activeFlows,
        count: activeFlows.length
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/research/flows/:flowId/stop - Stop a research flow
router.post('/flows/:flowId/stop',
  param('flowId').isString().notEmpty(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const flowId = req.params['flowId'];
      
      await mcpOrchestrator.stopResearchFlow(flowId);
      
      res.json({
        success: true,
        message: 'Research flow stopped successfully',
        flowId
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/research/flows/:flowId/status - Get flow status
router.get('/flows/:flowId/status',
  param('flowId').isString().notEmpty(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const flowId = req.params['flowId'];
      
      const status = await mcpOrchestrator.getFlowStatus(flowId);
      
      res.json({
        success: true,
        data: status
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/research/models - Get available AI models
router.get('/models',
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const models = await aiService.getAvailableModels();
      
      res.json({
        success: true,
        data: models
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/research/chat - Chat with AI research assistant
router.post('/chat',
  body('message').isString().notEmpty(),
  body('context').isArray().optional(),
  body('model').isString().optional(),
  body('temperature').isNumeric().optional(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const {
        message,
        context = [],
        model = 'gemma-3-4b-it-qat',
        temperature = 0.7
      } = req.body;

      logger.info('💬 AI research chat', { model, messageLength: message.length });

      const response = await aiService.chat(message, {
        context,
        model,
        temperature
      });

      res.json({
        success: true,
        data: response
      });
    } catch (error) {
      next(error);
    }
  }
);

export default router;
