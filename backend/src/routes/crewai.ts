import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { crewAIService, CrewAIResearchRequest } from '../services/CrewAIIntegrationService';
import { logger } from '../utils/logger';

const router = express.Router();

/**
 * @route POST /api/crewai/research/start
 * @desc Start a new CrewAI research flow
 * @access Public
 */
router.post('/research/start', [
  body('supplementName')
    .notEmpty()
    .withMessage('Supplement name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Supplement name must be between 2 and 100 characters'),
  body('researchDepth')
    .isIn(['basic', 'comprehensive', 'exhaustive'])
    .withMessage('Research depth must be basic, comprehensive, or exhaustive'),
  body('researchGoals')
    .isArray()
    .withMessage('Research goals must be an array'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Priority must be low, medium, high, or critical'),
  body('userId')
    .optional()
    .isString()
    .withMessage('User ID must be a string'),
  body('sessionId')
    .optional()
    .isString()
    .withMessage('Session ID must be a string')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const request: CrewAIResearchRequest = {
      supplementName: req.body.supplementName,
      researchDepth: req.body.researchDepth || 'comprehensive',
      researchGoals: req.body.researchGoals || [],
      priority: req.body.priority || 'medium',
      userId: req.body.userId,
      sessionId: req.body.sessionId
    };

    // Start the research flow
    const flowId = await crewAIService.startResearch(request);

    logger.info(`Started CrewAI research flow: ${flowId} for supplement: ${request.supplementName}`);

    res.status(201).json({
      success: true,
      message: 'Research flow started successfully',
      data: {
        flowId,
        supplementName: request.supplementName,
        researchDepth: request.researchDepth,
        estimatedCompletion: new Date(Date.now() + 10 * 60 * 1000) // 10 minutes
      }
    });

  } catch (error) {
    logger.error('Error starting CrewAI research:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to start research flow',
      error: error.message
    });
  }
});

/**
 * @route GET /api/crewai/research/:flowId/progress
 * @desc Get progress for a specific research flow
 * @access Public
 */
router.get('/research/:flowId/progress', [
  param('flowId')
    .notEmpty()
    .withMessage('Flow ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { flowId } = req.params;
    const progress = crewAIService.getFlowProgress(flowId);

    if (!progress) {
      return res.status(404).json({
        success: false,
        message: 'Research flow not found'
      });
    }

    res.json({
      success: true,
      data: progress
    });

  } catch (error) {
    logger.error('Error getting research progress:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get research progress',
      error: error.message
    });
  }
});

/**
 * @route GET /api/crewai/research/:flowId/result
 * @desc Get result for a completed research flow
 * @access Public
 */
router.get('/research/:flowId/result', [
  param('flowId')
    .notEmpty()
    .withMessage('Flow ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { flowId } = req.params;
    const result = crewAIService.getFlowResult(flowId);

    if (!result) {
      return res.status(404).json({
        success: false,
        message: 'Research result not found'
      });
    }

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    logger.error('Error getting research result:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get research result',
      error: error.message
    });
  }
});

/**
 * @route GET /api/crewai/research/active
 * @desc Get all active research flows
 * @access Public
 */
router.get('/research/active', async (req, res) => {
  try {
    const activeFlows = crewAIService.getActiveFlows();

    res.json({
      success: true,
      data: {
        count: activeFlows.length,
        flows: activeFlows
      }
    });

  } catch (error) {
    logger.error('Error getting active research flows:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get active research flows',
      error: error.message
    });
  }
});

/**
 * @route DELETE /api/crewai/research/:flowId
 * @desc Stop a running research flow
 * @access Public
 */
router.delete('/research/:flowId', [
  param('flowId')
    .notEmpty()
    .withMessage('Flow ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { flowId } = req.params;
    await crewAIService.stopResearch(flowId);

    logger.info(`Stopped CrewAI research flow: ${flowId}`);

    res.json({
      success: true,
      message: 'Research flow stopped successfully'
    });

  } catch (error) {
    logger.error('Error stopping research flow:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to stop research flow',
      error: error.message
    });
  }
});

/**
 * @route GET /api/crewai/agents/status
 * @desc Get status of all agents
 * @access Public
 */
router.get('/agents/status', async (req, res) => {
  try {
    const agentStatuses = crewAIService.getAgentStatuses();

    res.json({
      success: true,
      data: {
        count: agentStatuses.length,
        agents: agentStatuses
      }
    });

  } catch (error) {
    logger.error('Error getting agent statuses:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get agent statuses',
      error: error.message
    });
  }
});

/**
 * @route GET /api/crewai/agents/:agentId/status
 * @desc Get status of a specific agent
 * @access Public
 */
router.get('/agents/:agentId/status', [
  param('agentId')
    .notEmpty()
    .withMessage('Agent ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { agentId } = req.params;
    const agentStatus = crewAIService.getAgentStatus(agentId);

    if (!agentStatus) {
      return res.status(404).json({
        success: false,
        message: 'Agent not found'
      });
    }

    res.json({
      success: true,
      data: agentStatus
    });

  } catch (error) {
    logger.error('Error getting agent status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get agent status',
      error: error.message
    });
  }
});

/**
 * @route GET /api/crewai/health
 * @desc Health check for CrewAI service
 * @access Public
 */
router.get('/health', async (req, res) => {
  try {
    const isHealthy = await crewAIService.healthCheck();

    res.json({
      success: true,
      data: {
        status: isHealthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        service: 'CrewAI Integration Service'
      }
    });

  } catch (error) {
    logger.error('Error checking CrewAI health:', error);
    res.status(500).json({
      success: false,
      message: 'Health check failed',
      error: error.message
    });
  }
});

/**
 * @route GET /api/crewai/capabilities
 * @desc Get CrewAI service capabilities
 * @access Public
 */
router.get('/capabilities', async (req, res) => {
  try {
    const capabilities = {
      researchDepths: ['basic', 'comprehensive', 'exhaustive'],
      availableAgents: [
        {
          id: 'research_coordinator',
          name: 'Research Coordinator',
          role: 'Orchestrate research and synthesize findings',
          capabilities: ['project_management', 'evidence_synthesis', 'quality_assessment']
        },
        {
          id: 'literature_reviewer',
          name: 'Literature Review Specialist',
          role: 'Conduct comprehensive literature reviews',
          capabilities: ['pubmed_search', 'study_quality_assessment', 'evidence_grading']
        },
        {
          id: 'clinical_analyst',
          name: 'Clinical Trial Analyst',
          role: 'Analyze clinical trial data and methodology',
          capabilities: ['clinical_trials_search', 'statistical_analysis', 'bias_assessment']
        },
        {
          id: 'interaction_mapper',
          name: 'Interaction Mapping Expert',
          role: 'Identify drug-supplement interactions',
          capabilities: ['interaction_databases', 'pharmacokinetic_modeling', 'pathway_analysis']
        },
        {
          id: 'safety_assessor',
          name: 'Safety Assessment Specialist',
          role: 'Evaluate supplement safety profiles',
          capabilities: ['adverse_event_analysis', 'risk_assessment', 'population_safety']
        },
        {
          id: 'efficacy_evaluator',
          name: 'Efficacy Evaluation Specialist',
          role: 'Assess efficacy evidence using evidence-based medicine',
          capabilities: ['evidence_grading', 'effect_size_calculation', 'clinical_significance']
        },
        {
          id: 'market_analyst',
          name: 'Market Intelligence Analyst',
          role: 'Analyze market trends and consumer insights',
          capabilities: ['market_research', 'consumer_analysis', 'competitive_intelligence']
        },
        {
          id: 'regulatory_expert',
          name: 'Regulatory Compliance Expert',
          role: 'Evaluate regulatory status and compliance',
          capabilities: ['regulatory_databases', 'compliance_checking', 'quality_standards']
        }
      ],
      researchGoals: [
        'safety_profile',
        'efficacy_evidence',
        'drug_interactions',
        'dosage_guidelines',
        'clinical_trials',
        'market_intelligence',
        'regulatory_compliance',
        'user_reviews'
      ],
      supportedFormats: ['json', 'structured_report', 'executive_summary'],
      maxConcurrentFlows: 5,
      estimatedExecutionTimes: {
        basic: '3-5 minutes',
        comprehensive: '8-12 minutes',
        exhaustive: '15-20 minutes'
      }
    };

    res.json({
      success: true,
      data: capabilities
    });

  } catch (error) {
    logger.error('Error getting CrewAI capabilities:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get capabilities',
      error: error.message
    });
  }
});

export default router;