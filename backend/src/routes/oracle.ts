import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { AIService } from '@/services/AIService';
import { logger } from '@/utils/logger';
import { ValidationError } from '@/middleware/errorHandler';

const router = express.Router();
const aiService = new AIService();

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed: ' + errors.array().map(e => e.msg).join(', '));
  }
  next();
};

// POST /api/oracle/predictions - Generate AI predictions
router.post('/predictions',
  body('userProfile').isObject().notEmpty(),
  body('supplements').isArray().notEmpty(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const { userProfile, supplements } = req.body;

      logger.info('🔮 Generating AI predictions', { 
        userId: userProfile.id, 
        supplements: supplements.length 
      });

      // Generate predictions using LM Studio
      const predictionsPrompt = `
        Generate health predictions for a user with the following profile:
        
        User Profile: ${JSON.stringify(userProfile, null, 2)}
        Current Supplements: ${supplements.join(', ')}
        
        Generate 5-8 predictions covering:
        1. Health outcome predictions (3-6 months)
        2. Potential interaction risks
        3. Efficacy predictions for current supplements
        4. Optimization opportunities
        
        For each prediction, provide:
        - Type (health_outcome, interaction_risk, efficacy, optimization)
        - Title (clear, actionable)
        - Description (detailed explanation)
        - Confidence score (0-1)
        - Impact level (low, medium, high, critical)
        - Timeframe (when to expect results)
        - Evidence (supporting reasoning)
        - Actionable recommendations
        
        Return as JSON array of predictions.
      `;

      const response = await aiService.chat(predictionsPrompt, {
        model: 'gemma-3-4b-it-qat',
        temperature: 0.4
      });

      const predictions = parseAIResponse(response.message);

      res.json({
        success: true,
        predictions: Array.isArray(predictions) ? predictions : [predictions],
        userProfile: {
          id: userProfile.id,
          age: userProfile.age,
          healthGoals: userProfile.healthGoals
        },
        generatedAt: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/oracle/health-analysis - Analyze health data
router.post('/health-analysis',
  body('profile').isObject().notEmpty(),
  body('supplements').isArray().optional(),
  body('timestamp').isString().notEmpty(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const { profile, supplements = [], timestamp } = req.body;

      logger.info('🧠 Analyzing health data', { 
        profileId: profile.id,
        supplements: supplements.length 
      });

      // Analyze health data using LM Studio
      const analysisPrompt = `
        Conduct comprehensive health analysis for:
        
        Profile: ${JSON.stringify(profile, null, 2)}
        Supplements: ${supplements.join(', ')}
        Analysis Date: ${timestamp}
        
        Generate insights covering:
        1. Nutritional gaps and deficiencies
        2. Supplement optimization opportunities
        3. Lifestyle recommendations
        4. Health monitoring suggestions
        
        For each insight, provide:
        - Category (nutrition, supplementation, lifestyle, monitoring)
        - Title (clear, specific)
        - Insight (detailed analysis)
        - Priority (low, medium, high, urgent)
        - Personalized (true/false)
        - Sources count (estimated evidence strength)
        - Last updated timestamp
        
        Return as JSON array of insights.
      `;

      const response = await aiService.chat(analysisPrompt, {
        model: 'gemma-3-4b-it-qat',
        temperature: 0.3
      });

      const insights = parseAIResponse(response.message);

      res.json({
        success: true,
        insights: Array.isArray(insights) ? insights : [insights],
        analysisMetadata: {
          profileId: profile.id,
          supplementsAnalyzed: supplements.length,
          analysisDate: timestamp,
          model: 'gemma-3-4b-it-qat'
        }
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/oracle/recommendations - Get personalized recommendations
router.post('/recommendations',
  body('userId').isString().notEmpty(),
  body('context').isObject().optional(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const { userId, context = {} } = req.body;

      logger.info('⭐ Generating personalized recommendations', { userId });

      // Generate recommendations using LM Studio
      const recommendationsPrompt = `
        Generate personalized supplement recommendations for user: ${userId}
        
        Context: ${JSON.stringify(context, null, 2)}
        
        Create 5-10 recommendations focusing on:
        1. Evidence-based supplement suggestions
        2. Dosage optimization for current supplements
        3. Timing and combination strategies
        4. Budget-conscious alternatives
        5. Lifestyle integration tips
        
        For each recommendation, provide:
        - Title (clear, actionable)
        - Description (detailed explanation)
        - Priority (low, medium, high, urgent)
        - Evidence (supporting research)
        - Implementation steps
        - Expected timeline for results
        - Cost considerations
        
        Return as JSON array of recommendations.
      `;

      const response = await aiService.chat(recommendationsPrompt, {
        model: 'gemma-3-4b-it-qat',
        temperature: 0.5
      });

      const recommendations = parseAIResponse(response.message);

      res.json({
        success: true,
        recommendations: Array.isArray(recommendations) ? recommendations : [recommendations],
        userId,
        context,
        generatedAt: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/oracle/models - Get available AI models
router.get('/models',
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const models = await aiService.getAvailableModels();
      
      res.json({
        success: true,
        data: models,
        currentModel: 'gemma-3-4b-it-qat',
        lmStudioConnected: models.lmStudio && models.lmStudio.length > 0
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/oracle/chat - Chat with AI Oracle
router.post('/chat',
  body('message').isString().notEmpty(),
  body('context').isArray().optional(),
  body('mode').isIn(['predictions', 'insights', 'recommendations', 'general']).optional(),
  validateRequest,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const { message, context = [], mode = 'general' } = req.body;

      logger.info('💬 AI Oracle chat', { mode, messageLength: message.length });

      // Enhance prompt based on mode
      let enhancedPrompt = message;
      if (mode === 'predictions') {
        enhancedPrompt = `As a health prediction AI, ${message}. Focus on evidence-based predictions with confidence scores.`;
      } else if (mode === 'insights') {
        enhancedPrompt = `As a health insights AI, ${message}. Provide actionable, personalized insights.`;
      } else if (mode === 'recommendations') {
        enhancedPrompt = `As a supplement recommendation AI, ${message}. Give specific, evidence-based recommendations.`;
      }

      const response = await aiService.chat(enhancedPrompt, {
        context,
        model: 'gemma-3-4b-it-qat',
        temperature: 0.6
      });

      res.json({
        success: true,
        data: response,
        mode,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }
);

// Helper function to parse AI responses
function parseAIResponse(response: string): any {
  try {
    // Try to extract JSON from the response
    const jsonMatch = response.match(/\{[\s\S]*\}|\[[\s\S]*\]/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    
    // If no JSON found, return structured response
    return {
      content: response,
      type: 'text_response',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    logger.warn('Failed to parse AI response as JSON', { 
      response: response.substring(0, 200),
      error: error.message 
    });
    
    return {
      content: response,
      type: 'text_response',
      parseError: true,
      timestamp: new Date().toISOString()
    };
  }
}

export default router;
