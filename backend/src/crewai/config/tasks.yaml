literature_review_task:
  description: >
    Conduct a comprehensive literature review for {supplement_name}.
    Search peer-reviewed databases including PubMed, Cochrane Library, and Google Scholar.
    Focus on:
    1. Systematic reviews and meta-analyses
    2. Randomized controlled trials
    3. Observational studies
    4. Mechanism of action studies
    5. Safety and toxicology studies
    
    Analyze study quality using appropriate assessment tools and synthesize findings
    into a comprehensive evidence summary with confidence ratings.
  expected_output: >
    A detailed literature review report containing:
    - Summary of search strategy and databases used
    - List of included studies with quality assessments
    - Evidence synthesis organized by health outcomes
    - Strength of evidence ratings
    - Identified research gaps and limitations
    - Recommendations for future research
  agent: literature_review_specialist
  tools:
    - pubmed_search_tool
    - cochrane_search_tool
    - google_scholar_tool
    - study_quality_assessment_tool

clinical_trial_analysis_task:
  description: >
    Analyze clinical trial data for {supplement_name} focusing on:
    1. Study design quality and methodology
    2. Primary and secondary endpoints
    3. Statistical significance and clinical significance
    4. Effect sizes and confidence intervals
    5. Population characteristics and generalizability
    6. Bias assessment and limitations
    
    Evaluate trials using established quality criteria (Jadad scale, Cochrane Risk of Bias tool)
    and provide evidence-based conclusions about efficacy.
  expected_output: >
    A comprehensive clinical trial analysis report including:
    - Detailed analysis of individual trials
    - Quality assessment scores for each study
    - Meta-analysis results where appropriate
    - Forest plots and statistical summaries
    - Clinical significance assessment
    - Population-specific findings
    - Recommendations based on evidence strength
  agent: clinical_trial_analyst
  tools:
    - clinical_trials_search_tool
    - statistical_analysis_tool
    - bias_assessment_tool
    - meta_analysis_tool

interaction_mapping_task:
  description: >
    Identify and analyze potential interactions for {supplement_name}:
    1. Drug-supplement interactions
    2. Supplement-supplement interactions
    3. Food-supplement interactions
    4. Mechanism-based interaction predictions
    5. Clinical significance assessment
    6. Population-specific considerations
    
    Use interaction databases, pharmacokinetic models, and molecular pathway analysis
    to provide comprehensive interaction profiles.
  expected_output: >
    A detailed interaction analysis report containing:
    - Comprehensive interaction matrix
    - Mechanism of interaction explanations
    - Clinical significance ratings
    - Population-specific warnings
    - Monitoring recommendations
    - Contraindications and precautions
    - Evidence quality assessment for each interaction
  agent: interaction_mapping_expert
  tools:
    - drug_interaction_database_tool
    - pharmacokinetic_modeling_tool
    - pathway_analysis_tool
    - interaction_prediction_tool

safety_assessment_task:
  description: >
    Conduct comprehensive safety assessment for {supplement_name}:
    1. Adverse event analysis from databases and literature
    2. Dose-response relationships
    3. Population-specific safety considerations
    4. Long-term safety data evaluation
    5. Contraindications and warnings
    6. Risk-benefit analysis
    
    Analyze safety data from multiple sources including FDA FAERS, WHO VigiBase,
    and published case reports.
  expected_output: >
    A comprehensive safety assessment report including:
    - Adverse event profile with frequency data
    - Dose-response safety relationships
    - Population-specific safety considerations
    - Contraindications and precautions
    - Risk stratification guidelines
    - Monitoring recommendations
    - Risk-benefit analysis summary
  agent: safety_assessment_specialist
  tools:
    - adverse_event_database_tool
    - safety_signal_detection_tool
    - risk_assessment_tool
    - population_safety_tool

efficacy_evaluation_task:
  description: >
    Evaluate efficacy evidence for {supplement_name} health claims:
    1. Systematic evaluation of health claims
    2. Evidence grading using GRADE methodology
    3. Effect size calculations and clinical significance
    4. Dose-response relationships
    5. Population-specific efficacy
    6. Comparison with standard treatments
    
    Apply evidence-based medicine principles to assess the strength of efficacy evidence.
  expected_output: >
    A detailed efficacy evaluation report containing:
    - Health claim-specific evidence summaries
    - GRADE evidence quality ratings
    - Effect size calculations with confidence intervals
    - Number needed to treat (NNT) where applicable
    - Dose-response efficacy relationships
    - Population-specific efficacy data
    - Comparison with alternative treatments
    - Clinical practice recommendations
  agent: efficacy_evaluation_specialist
  tools:
    - evidence_grading_tool
    - effect_size_calculator
    - dose_response_analyzer
    - clinical_significance_tool

market_intelligence_task:
  description: >
    Analyze market intelligence for {supplement_name}:
    1. Market size and growth trends
    2. Consumer usage patterns and demographics
    3. Product formulations and pricing analysis
    4. Consumer reviews and satisfaction data
    5. Competitive landscape analysis
    6. Regulatory and market access considerations
    
    Provide insights into commercial aspects and real-world usage patterns.
  expected_output: >
    A comprehensive market intelligence report including:
    - Market size and growth projections
    - Consumer demographic analysis
    - Usage pattern insights
    - Product formulation trends
    - Pricing analysis and value propositions
    - Consumer satisfaction metrics
    - Competitive positioning analysis
    - Market access and regulatory insights
  agent: market_intelligence_analyst
  tools:
    - market_research_tool
    - consumer_review_analyzer
    - pricing_analysis_tool
    - competitive_intelligence_tool

regulatory_compliance_task:
  description: >
    Evaluate regulatory compliance status for {supplement_name}:
    1. FDA DSHEA compliance requirements
    2. EU Novel Food regulation status
    3. International regulatory frameworks
    4. Manufacturing and quality standards
    5. Labeling and marketing claim regulations
    6. Import/export requirements
    
    Provide comprehensive regulatory guidance for different markets.
  expected_output: >
    A detailed regulatory compliance report including:
    - Regulatory status by jurisdiction
    - Compliance requirements summary
    - Manufacturing quality standards
    - Labeling and marketing guidelines
    - Import/export considerations
    - Regulatory risk assessment
    - Compliance recommendations
    - Regulatory pathway guidance
  agent: regulatory_compliance_expert
  tools:
    - regulatory_database_tool
    - compliance_checker_tool
    - quality_standards_tool
    - labeling_guidelines_tool

research_synthesis_task:
  description: >
    Synthesize all research findings for {supplement_name} into a comprehensive report:
    1. Integrate findings from all specialized analyses
    2. Identify consensus and conflicting evidence
    3. Provide overall evidence quality assessment
    4. Generate actionable recommendations
    5. Highlight key safety considerations
    6. Suggest areas for future research
    
    Create a master report that provides a complete picture of the supplement's
    evidence base, safety profile, and clinical utility.
  expected_output: >
    A comprehensive master research report containing:
    - Executive summary with key findings
    - Integrated evidence synthesis
    - Overall evidence quality assessment
    - Clinical practice recommendations
    - Safety guidelines and contraindications
    - Population-specific considerations
    - Research gaps and future directions
    - Confidence ratings for all conclusions
    - References and evidence sources
  agent: supplement_research_coordinator
  context:
    - literature_review_task
    - clinical_trial_analysis_task
    - interaction_mapping_task
    - safety_assessment_task
    - efficacy_evaluation_task
    - market_intelligence_task
    - regulatory_compliance_task
  tools:
    - evidence_synthesis_tool
    - report_generator_tool
    - quality_assessment_tool
    - recommendation_engine_tool