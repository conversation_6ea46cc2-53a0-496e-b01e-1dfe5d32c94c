"""
CrewAI Supplement Research Crew
Ultra-powerful autonomous research system for comprehensive supplement analysis
"""

import os
import yaml
from typing import Dict, List, Any, Optional
from crewai import Agent, Task, Crew, Process
from crewai.project import CrewBase, agent, crew, task
from langchain_openai import ChatOpenAI
from langchain_ollama import ChatOllama

# Import custom tools
from tools.research_tools import (
    PubMedSearchTool,
    ClinicalTrialsSearchTool,
    DrugInteractionDatabaseTool,
    AdverseEventDatabaseTool,
    EvidenceGradingTool,
    MarketResearchTool,
    RegulatoryDatabaseTool,
    EvidenceSynthesisTool
)


@CrewBase
class SupplementResearchCrew:
    """CrewAI Supplement Research Crew for autonomous research"""
    
    agents_config = 'config/agents.yaml'
    tasks_config = 'config/tasks.yaml'
    
    def __init__(self, supplement_name: str, research_depth: str = "comprehensive"):
        self.supplement_name = supplement_name
        self.research_depth = research_depth
        self.llm = self._initialize_llm()
        
    def _initialize_llm(self):
        """Initialize the language model"""
        # Try to use local Ollama first, fallback to OpenAI
        try:
            return ChatOllama(
                model="llama3.1:8b",
                base_url="http://localhost:11434"
            )
        except:
            # Fallback to OpenAI if available
            if os.getenv("OPENAI_API_KEY"):
                return ChatOpenAI(
                    model="gpt-4",
                    temperature=0.1
                )
            else:
                # Use a mock LLM for testing
                return None

    @agent
    def supplement_research_coordinator(self) -> Agent:
        return Agent(
            config=self.agents_config['supplement_research_coordinator'],
            llm=self.llm,
            tools=[
                EvidenceSynthesisTool(),
                EvidenceGradingTool()
            ],
            verbose=True
        )

    @agent
    def literature_review_specialist(self) -> Agent:
        return Agent(
            config=self.agents_config['literature_review_specialist'],
            llm=self.llm,
            tools=[
                PubMedSearchTool(),
                EvidenceGradingTool()
            ],
            verbose=True
        )

    @agent
    def clinical_trial_analyst(self) -> Agent:
        return Agent(
            config=self.agents_config['clinical_trial_analyst'],
            llm=self.llm,
            tools=[
                ClinicalTrialsSearchTool(),
                EvidenceGradingTool()
            ],
            verbose=True
        )

    @agent
    def interaction_mapping_expert(self) -> Agent:
        return Agent(
            config=self.agents_config['interaction_mapping_expert'],
            llm=self.llm,
            tools=[
                DrugInteractionDatabaseTool()
            ],
            verbose=True
        )

    @agent
    def safety_assessment_specialist(self) -> Agent:
        return Agent(
            config=self.agents_config['safety_assessment_specialist'],
            llm=self.llm,
            tools=[
                AdverseEventDatabaseTool(),
                EvidenceGradingTool()
            ],
            verbose=True
        )

    @agent
    def efficacy_evaluation_specialist(self) -> Agent:
        return Agent(
            config=self.agents_config['efficacy_evaluation_specialist'],
            llm=self.llm,
            tools=[
                EvidenceGradingTool(),
                EvidenceSynthesisTool()
            ],
            verbose=True
        )

    @agent
    def market_intelligence_analyst(self) -> Agent:
        return Agent(
            config=self.agents_config['market_intelligence_analyst'],
            llm=self.llm,
            tools=[
                MarketResearchTool()
            ],
            verbose=True
        )

    @agent
    def regulatory_compliance_expert(self) -> Agent:
        return Agent(
            config=self.agents_config['regulatory_compliance_expert'],
            llm=self.llm,
            tools=[
                RegulatoryDatabaseTool()
            ],
            verbose=True
        )

    @task
    def literature_review_task(self) -> Task:
        return Task(
            config=self.tasks_config['literature_review_task'],
            agent=self.literature_review_specialist(),
            context={
                'supplement_name': self.supplement_name,
                'research_depth': self.research_depth
            }
        )

    @task
    def clinical_trial_analysis_task(self) -> Task:
        return Task(
            config=self.tasks_config['clinical_trial_analysis_task'],
            agent=self.clinical_trial_analyst(),
            context={
                'supplement_name': self.supplement_name,
                'research_depth': self.research_depth
            }
        )

    @task
    def interaction_mapping_task(self) -> Task:
        return Task(
            config=self.tasks_config['interaction_mapping_task'],
            agent=self.interaction_mapping_expert(),
            context={
                'supplement_name': self.supplement_name
            }
        )

    @task
    def safety_assessment_task(self) -> Task:
        return Task(
            config=self.tasks_config['safety_assessment_task'],
            agent=self.safety_assessment_specialist(),
            context={
                'supplement_name': self.supplement_name
            }
        )

    @task
    def efficacy_evaluation_task(self) -> Task:
        return Task(
            config=self.tasks_config['efficacy_evaluation_task'],
            agent=self.efficacy_evaluation_specialist(),
            context={
                'supplement_name': self.supplement_name
            }
        )

    @task
    def market_intelligence_task(self) -> Task:
        return Task(
            config=self.tasks_config['market_intelligence_task'],
            agent=self.market_intelligence_analyst(),
            context={
                'supplement_name': self.supplement_name
            }
        )

    @task
    def regulatory_compliance_task(self) -> Task:
        return Task(
            config=self.tasks_config['regulatory_compliance_task'],
            agent=self.regulatory_compliance_expert(),
            context={
                'supplement_name': self.supplement_name
            }
        )

    @task
    def research_synthesis_task(self) -> Task:
        return Task(
            config=self.tasks_config['research_synthesis_task'],
            agent=self.supplement_research_coordinator(),
            context={
                'supplement_name': self.supplement_name,
                'research_depth': self.research_depth
            }
        )

    @crew
    def crew(self) -> Crew:
        """Creates the Supplement Research crew"""
        
        # Define task dependencies based on research depth
        if self.research_depth == "basic":
            tasks = [
                self.literature_review_task(),
                self.safety_assessment_task(),
                self.research_synthesis_task()
            ]
        elif self.research_depth == "comprehensive":
            tasks = [
                self.literature_review_task(),
                self.clinical_trial_analysis_task(),
                self.interaction_mapping_task(),
                self.safety_assessment_task(),
                self.efficacy_evaluation_task(),
                self.research_synthesis_task()
            ]
        else:  # exhaustive
            tasks = [
                self.literature_review_task(),
                self.clinical_trial_analysis_task(),
                self.interaction_mapping_task(),
                self.safety_assessment_task(),
                self.efficacy_evaluation_task(),
                self.market_intelligence_task(),
                self.regulatory_compliance_task(),
                self.research_synthesis_task()
            ]
        
        return Crew(
            agents=self.agents,
            tasks=tasks,
            process=Process.sequential,
            verbose=2,
            memory=True,
            embedder={
                "provider": "ollama",
                "config": {
                    "model": "nomic-embed-text"
                }
            }
        )

    def kickoff(self, inputs: Dict[str, Any] = None) -> str:
        """Execute the research crew"""
        if inputs is None:
            inputs = {
                'supplement_name': self.supplement_name,
                'research_depth': self.research_depth
            }
        
        result = self.crew().kickoff(inputs=inputs)
        return result


class SupplementResearchOrchestrator:
    """Orchestrator for managing multiple research crews and flows"""
    
    def __init__(self):
        self.active_crews: Dict[str, SupplementResearchCrew] = {}
        self.research_history: List[Dict[str, Any]] = []
    
    def start_research(
        self, 
        supplement_name: str, 
        research_depth: str = "comprehensive",
        research_goals: List[str] = None
    ) -> str:
        """Start a new research crew for a supplement"""
        
        crew_id = f"crew_{supplement_name}_{len(self.active_crews)}"
        
        # Create and configure the crew
        crew = SupplementResearchCrew(
            supplement_name=supplement_name,
            research_depth=research_depth
        )
        
        self.active_crews[crew_id] = crew
        
        # Execute the research
        try:
            result = crew.kickoff({
                'supplement_name': supplement_name,
                'research_depth': research_depth,
                'research_goals': research_goals or []
            })
            
            # Store in history
            self.research_history.append({
                'crew_id': crew_id,
                'supplement_name': supplement_name,
                'research_depth': research_depth,
                'result': result,
                'timestamp': str(datetime.now())
            })
            
            # Clean up active crew
            del self.active_crews[crew_id]
            
            return result
            
        except Exception as e:
            # Clean up on error
            if crew_id in self.active_crews:
                del self.active_crews[crew_id]
            raise e
    
    def get_active_crews(self) -> Dict[str, Dict[str, Any]]:
        """Get information about active research crews"""
        return {
            crew_id: {
                'supplement_name': crew.supplement_name,
                'research_depth': crew.research_depth,
                'status': 'running'
            }
            for crew_id, crew in self.active_crews.items()
        }
    
    def get_research_history(self, supplement_name: str = None) -> List[Dict[str, Any]]:
        """Get research history, optionally filtered by supplement"""
        if supplement_name:
            return [
                entry for entry in self.research_history
                if entry['supplement_name'].lower() == supplement_name.lower()
            ]
        return self.research_history


# Global orchestrator instance
research_orchestrator = SupplementResearchOrchestrator()


def create_research_crew(supplement_name: str, research_depth: str = "comprehensive") -> SupplementResearchCrew:
    """Factory function to create a research crew"""
    return SupplementResearchCrew(supplement_name, research_depth)


if __name__ == "__main__":
    # Example usage
    crew = create_research_crew("Vitamin D", "comprehensive")
    result = crew.kickoff()
    print(result)