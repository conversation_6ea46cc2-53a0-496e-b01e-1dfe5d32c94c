import { logger } from '../utils/logger';
import { AIService } from './AIService';
import { config } from '../config/environment';

export interface MCPResearchRequest {
  supplementName: string;
  researchGoals: string[];
  researchDepth: 'basic' | 'comprehensive' | 'exhaustive';
  sources: ResearchSource[];
  mcpTools: MCPTool[];
  qualityThreshold: number;
}

// Enhanced interfaces for LM Studio integration
export interface EnhancedResearchRequest {
  flowId: string;
  supplement: string;
  goals: string[];
  depth: 'basic' | 'comprehensive' | 'exhaustive';
  includeInteractions: boolean;
  includeClinicalTrials: boolean;
  includeUserReviews: boolean;
  userId?: string;
  model?: string;
}

export interface ResearchFlow {
  id: string;
  name: string;
  status: 'planning' | 'executing' | 'completed' | 'error';
  progress: number;
  startTime: Date;
  estimatedCompletion?: Date;
  agents: AgentStatus[];
  currentStep: string;
  totalSteps: number;
  completedSteps: number;
  results: ResearchResult[];
}

export interface AgentStatus {
  id: string;
  name: string;
  status: 'idle' | 'working' | 'completed' | 'error';
  currentTask?: string;
  progress: number;
  lastUpdate: Date;
  metrics: {
    tasksCompleted: number;
    averageTime: number;
    successRate: number;
  };
}

export interface ResearchResult {
  id: string;
  type: 'literature' | 'clinical' | 'safety' | 'interaction' | 'user_review';
  title: string;
  content: string;
  confidence: number;
  sources: string[];
  timestamp: Date;
  relevanceScore: number;
}

export interface ResearchSource {
  type: 'web_search' | 'crawling' | 'extraction' | 'mapping';
  domains: string[];
  priority: number;
  parameters: Record<string, any>;
}

export interface MCPTool {
  name: 'tavily-search' | 'tavily-crawl' | 'tavily-extract' | 'tavily-map' | 'brave-search' | 'context7';
  enabled: boolean;
  config: Record<string, any>;
}

export interface MCPResearchResult {
  supplementName: string;
  researchId: string;
  findings: ResearchFinding[];
  sources: SourceAnalysis[];
  qualityMetrics: QualityMetrics;
  executionMetrics: ExecutionMetrics;
  timestamp: Date;
}

export interface ResearchFinding {
  category: 'benefits' | 'risks' | 'interactions' | 'dosage' | 'mechanisms' | 'contraindications';
  title: string;
  description: string;
  evidence: Evidence[];
  confidence: number;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  population?: string;
  sourceCount: number;
}

export interface Evidence {
  type: 'clinical_trial' | 'meta_analysis' | 'systematic_review' | 'observational_study' | 'case_report';
  title: string;
  url: string;
  authors?: string[];
  journal?: string;
  publicationDate?: Date;
  sampleSize?: number;
  studyDesign?: string;
  keyFindings: string[];
  limitations?: string[];
  qualityScore: number;
}

export interface SourceAnalysis {
  url: string;
  domain: string;
  title: string;
  contentType: string;
  credibilityScore: number;
  relevanceScore: number;
  freshnessScore: number;
  extractedData: ExtractedData;
  mcpToolUsed: string;
  processingTime: number;
}

export interface ExtractedData {
  mainContent: string;
  structuredData: StructuredContent;
  images: ExtractedImage[];
  links: ExtractedLink[];
  metadata: ContentMetadata;
}

export interface StructuredContent {
  headings: ContentHeading[];
  paragraphs: ContentParagraph[];
  lists: ContentList[];
  tables: ContentTable[];
  citations: Citation[];
}

export interface ContentHeading {
  level: number;
  text: string;
  relevanceScore: number;
}

export interface ContentParagraph {
  text: string;
  relevanceScore: number;
  keyTerms: string[];
}

export interface ContentList {
  type: 'ordered' | 'unordered';
  items: string[];
  relevanceScore: number;
}

export interface ContentTable {
  headers: string[];
  rows: string[][];
  caption?: string;
  relevanceScore: number;
}

export interface Citation {
  text: string;
  url?: string;
  type: 'reference' | 'study' | 'guideline';
}

export interface ExtractedImage {
  url: string;
  alt: string;
  caption?: string;
  relevanceScore: number;
}

export interface ExtractedLink {
  url: string;
  text: string;
  type: 'internal' | 'external' | 'reference';
  relevanceScore: number;
}

export interface ContentMetadata {
  author?: string;
  publicationDate?: Date;
  lastModified?: Date;
  language: string;
  wordCount: number;
  readingTime: number;
}

export interface QualityMetrics {
  overallScore: number;
  completeness: number;
  accuracy: number;
  relevance: number;
  credibility: number;
  freshness: number;
  diversity: number;
  evidenceStrength: number;
}

export interface ExecutionMetrics {
  totalExecutionTime: number;
  mcpToolsUsed: string[];
  sourcesAnalyzed: number;
  dataExtracted: number;
  apiCallsCount: number;
  cacheHitRate: number;
  errorRate: number;
}

export class MCPResearchOrchestrator {
  private activeResearch: Map<string, MCPResearchRequest> = new Map();
  private researchHistory: Map<string, MCPResearchResult[]> = new Map();
  private mcpToolsConfig: Map<string, any> = new Map();

  // Enhanced properties for LM Studio integration
  private activeFlows: Map<string, ResearchFlow> = new Map();
  private aiService: AIService;

  constructor() {
    this.initializeMCPTools();
    this.aiService = new AIService();
  }

  /**
   * Initialize MCP tools configuration
   */
  private initializeMCPTools(): void {
    // Tavily MCP configuration
    this.mcpToolsConfig.set('tavily-search', {
      maxResults: 20,
      searchDepth: 'advanced',
      includeRawContent: true,
      includeImages: true,
      timeRange: 'year'
    });

    this.mcpToolsConfig.set('tavily-crawl', {
      maxDepth: 3,
      maxBreadth: 20,
      limit: 50,
      extractDepth: 'advanced',
      allowExternal: false
    });

    this.mcpToolsConfig.set('tavily-extract', {
      extractDepth: 'advanced',
      includeImages: true
    });

    this.mcpToolsConfig.set('tavily-map', {
      maxDepth: 2,
      maxBreadth: 15,
      limit: 30
    });

    // Brave Search configuration
    this.mcpToolsConfig.set('brave-search', {
      count: 20,
      offset: 0,
      freshness: 'year'
    });

    // Context7 configuration
    this.mcpToolsConfig.set('context7', {
      tokens: 10000,
      includeCodeSnippets: true
    });

    logger.info('MCP Research Orchestrator initialized with 6 tools');
  }

  /**
   * Execute comprehensive research using MCP tools
   */
  async executeResearch(request: MCPResearchRequest): Promise<MCPResearchResult> {
    const researchId = `research-${Date.now()}`;
    const startTime = Date.now();

    try {
      logger.info(`Starting MCP research for: ${request.supplementName}`);
      
      this.activeResearch.set(researchId, request);

      // Phase 1: Multi-source data collection
      const collectedData = await this.collectDataFromSources(request);
      
      // Phase 2: Advanced content extraction
      const extractedContent = await this.extractAndAnalyzeContent(collectedData, request);
      
      // Phase 3: Intelligent synthesis and analysis
      const synthesizedFindings = await this.synthesizeFindings(extractedContent, request);
      
      // Phase 4: Quality assessment and validation
      const qualityMetrics = await this.assessQuality(synthesizedFindings, extractedContent);
      
      // Phase 5: Result compilation
      const result: MCPResearchResult = {
        supplementName: request.supplementName,
        researchId,
        findings: synthesizedFindings,
        sources: this.analyzeSourceQuality(collectedData),
        qualityMetrics,
        executionMetrics: {
          totalExecutionTime: Date.now() - startTime,
          mcpToolsUsed: request.mcpTools.filter(t => t.enabled).map(t => t.name),
          sourcesAnalyzed: collectedData.length,
          dataExtracted: extractedContent.length,
          apiCallsCount: this.calculateApiCalls(request),
          cacheHitRate: 0.75, // Mock value
          errorRate: 0.05 // Mock value
        },
        timestamp: new Date()
      };

      // Store result
      this.storeResearchResult(request.supplementName, result);
      this.activeResearch.delete(researchId);

      logger.info(`MCP research completed in ${result.executionMetrics.totalExecutionTime}ms`);
      return result;

    } catch (error) {
      logger.error('MCP research failed:', error);
      this.activeResearch.delete(researchId);
      throw new Error(`Research failed: ${error.message}`);
    }
  }

  /**
   * Collect data from multiple sources using MCP tools
   */
  private async collectDataFromSources(request: MCPResearchRequest): Promise<any[]> {
    const collectedData: any[] = [];
    
    for (const tool of request.mcpTools.filter(t => t.enabled)) {
      try {
        switch (tool.name) {
          case 'tavily-search':
            const searchData = await this.executeTavilySearch(request, tool.config);
            collectedData.push(...searchData);
            break;
            
          case 'tavily-crawl':
            const crawlData = await this.executeTavilyCrawl(request, tool.config);
            collectedData.push(...crawlData);
            break;
            
          case 'brave-search':
            const braveData = await this.executeBraveSearch(request, tool.config);
            collectedData.push(...braveData);
            break;
            
          case 'context7':
            const context7Data = await this.executeContext7Search(request, tool.config);
            collectedData.push(...context7Data);
            break;
        }
      } catch (error) {
        logger.warn(`Failed to collect data using ${tool.name}:`, error);
      }
    }
    
    return collectedData;
  }

  /**
   * Execute Tavily search
   */
  private async executeTavilySearch(request: MCPResearchRequest, config: any): Promise<any[]> {
    // This would integrate with actual Tavily MCP
    // For now, return mock data
    return [
      {
        source: 'tavily-search',
        url: 'https://example.com/supplement-study',
        title: `Clinical study of ${request.supplementName}`,
        content: 'Mock content from Tavily search',
        relevanceScore: 0.9,
        credibilityScore: 0.85
      }
    ];
  }

  /**
   * Execute Tavily crawl
   */
  private async executeTavilyCrawl(request: MCPResearchRequest, config: any): Promise<any[]> {
    // This would integrate with actual Tavily MCP crawl
    return [
      {
        source: 'tavily-crawl',
        url: 'https://example.com/supplement-info',
        title: `Comprehensive ${request.supplementName} information`,
        content: 'Mock content from Tavily crawl',
        relevanceScore: 0.88,
        credibilityScore: 0.82
      }
    ];
  }

  /**
   * Execute Brave search
   */
  private async executeBraveSearch(request: MCPResearchRequest, config: any): Promise<any[]> {
    // This would integrate with actual Brave Search MCP
    return [
      {
        source: 'brave-search',
        url: 'https://example.com/supplement-research',
        title: `${request.supplementName} research findings`,
        content: 'Mock content from Brave search',
        relevanceScore: 0.85,
        credibilityScore: 0.80
      }
    ];
  }

  /**
   * Execute Context7 search
   */
  private async executeContext7Search(request: MCPResearchRequest, config: any): Promise<any[]> {
    // This would integrate with actual Context7 MCP
    return [
      {
        source: 'context7',
        url: 'https://docs.example.com/supplement-guide',
        title: `${request.supplementName} documentation`,
        content: 'Mock content from Context7',
        relevanceScore: 0.92,
        credibilityScore: 0.88
      }
    ];
  }

  // Additional helper methods
  private async extractAndAnalyzeContent(data: any[], request: MCPResearchRequest): Promise<any[]> {
    // Extract and analyze content using AI
    return data.map(item => ({
      ...item,
      extractedData: {
        mainContent: item.content,
        keyTerms: this.extractKeyTerms(item.content, request.supplementName),
        sentiment: this.analyzeSentiment(item.content),
        entities: this.extractEntities(item.content)
      }
    }));
  }

  private async synthesizeFindings(content: any[], request: MCPResearchRequest): Promise<ResearchFinding[]> {
    // Synthesize findings from extracted content
    return [
      {
        category: 'benefits',
        title: `Potential benefits of ${request.supplementName}`,
        description: 'Multiple studies suggest positive effects',
        evidence: [],
        confidence: 0.85,
        sourceCount: content.length
      }
    ];
  }

  private async assessQuality(findings: ResearchFinding[], content: any[]): Promise<QualityMetrics> {
    return {
      overallScore: 0.85,
      completeness: 0.88,
      accuracy: 0.82,
      relevance: 0.90,
      credibility: 0.85,
      freshness: 0.78,
      diversity: 0.83,
      evidenceStrength: 0.87
    };
  }

  private analyzeSourceQuality(data: any[]): SourceAnalysis[] {
    return data.map(item => ({
      url: item.url,
      domain: new URL(item.url).hostname,
      title: item.title,
      contentType: 'text/html',
      credibilityScore: item.credibilityScore || 0.8,
      relevanceScore: item.relevanceScore || 0.8,
      freshnessScore: 0.8,
      extractedData: item.extractedData || {},
      mcpToolUsed: item.source,
      processingTime: 1000
    }));
  }

  private extractKeyTerms(content: string, supplementName: string): string[] {
    // Extract key terms using NLP
    return [supplementName, 'dosage', 'benefits', 'side effects'];
  }

  private analyzeSentiment(content: string): number {
    // Analyze sentiment
    return 0.7; // Mock positive sentiment
  }

  private extractEntities(content: string): any[] {
    // Extract entities
    return [];
  }

  private calculateApiCalls(request: MCPResearchRequest): number {
    return request.mcpTools.filter(t => t.enabled).length * 5; // Mock calculation
  }

  private storeResearchResult(supplementName: string, result: MCPResearchResult): void {
    if (!this.researchHistory.has(supplementName)) {
      this.researchHistory.set(supplementName, []);
    }
    this.researchHistory.get(supplementName)!.push(result);
  }

  /**
   * Get research history for a supplement
   */
  getResearchHistory(supplementName: string): MCPResearchResult[] {
    return this.researchHistory.get(supplementName) || [];
  }

  /**
   * Enhanced autonomous research with LM Studio integration
   */
  async startAutonomousResearch(request: EnhancedResearchRequest): Promise<string> {
    const { flowId, supplement, goals, depth, model = 'gemma-3-4b-it-qat' } = request;

    logger.info('🚀 Starting enhanced autonomous research', { flowId, supplement, model });

    // Create research flow
    const flow: ResearchFlow = {
      id: flowId,
      name: `Research: ${supplement}`,
      status: 'planning',
      progress: 0,
      startTime: new Date(),
      agents: this.createAgentTeam(request),
      currentStep: 'Initializing AI research agents',
      totalSteps: this.calculateTotalSteps(request),
      completedSteps: 0,
      results: []
    };

    this.activeFlows.set(flowId, flow);

    // Start autonomous research process
    this.executeAutonomousResearch(flowId, request).catch(error => {
      logger.error('Autonomous research failed', error, { flowId });
      this.updateFlowStatus(flowId, 'error');
    });

    return flowId;
  }

  /**
   * Execute autonomous research using LM Studio
   */
  private async executeAutonomousResearch(flowId: string, request: EnhancedResearchRequest): Promise<void> {
    const flow = this.activeFlows.get(flowId);
    if (!flow) return;

    try {
      // Phase 1: Research Planning with AI
      this.updateFlowProgress(flowId, 10, 'AI research planning');
      const researchPlan = await this.generateResearchPlan(request);

      // Phase 2: Literature Review Agent
      this.updateFlowProgress(flowId, 25, 'Literature review in progress');
      const literatureResults = await this.executeLiteratureReview(request, researchPlan);

      // Phase 3: Clinical Analysis Agent
      this.updateFlowProgress(flowId, 45, 'Clinical trial analysis');
      const clinicalResults = await this.executeClinicalAnalysis(request, researchPlan);

      // Phase 4: Safety Assessment Agent
      this.updateFlowProgress(flowId, 65, 'Safety profile assessment');
      const safetyResults = await this.executeSafetyAssessment(request, researchPlan);

      // Phase 5: Interaction Analysis (if requested)
      if (request.includeInteractions) {
        this.updateFlowProgress(flowId, 80, 'Interaction analysis');
        const interactionResults = await this.executeInteractionAnalysis(request, researchPlan);
        flow.results.push(...interactionResults);
      }

      // Phase 6: Synthesis and Final Analysis
      this.updateFlowProgress(flowId, 95, 'Synthesizing findings');
      const synthesizedResults = await this.synthesizeResearchResults(
        [...literatureResults, ...clinicalResults, ...safetyResults],
        request
      );

      // Complete the research
      flow.results = synthesizedResults;
      this.updateFlowStatus(flowId, 'completed', 100);

      logger.info('✅ Autonomous research completed', { flowId, resultsCount: synthesizedResults.length });

    } catch (error) {
      logger.error('Autonomous research execution failed', error, { flowId });
      this.updateFlowStatus(flowId, 'error');
    }
  }

  /**
   * Generate research plan using LM Studio
   */
  private async generateResearchPlan(request: EnhancedResearchRequest): Promise<any> {
    const prompt = `
      Create a comprehensive research plan for ${request.supplement}.

      Research Goals: ${request.goals.join(', ')}
      Depth Level: ${request.depth}
      Include Interactions: ${request.includeInteractions}
      Include Clinical Trials: ${request.includeClinicalTrials}

      Generate a structured research plan with:
      1. Key research questions
      2. Search strategies and keywords
      3. Evidence hierarchy priorities
      4. Safety considerations to investigate
      5. Interaction patterns to analyze

      Return as structured JSON.
    `;

    const response = await this.aiService.chat(prompt, {
      model: request.model || 'gemma-3-4b-it-qat',
      temperature: 0.3
    });

    return this.parseAIResponse(response.message);
  }

  /**
   * Execute literature review using AI
   */
  private async executeLiteratureReview(request: EnhancedResearchRequest, plan: any): Promise<ResearchResult[]> {
    const prompt = `
      Conduct a comprehensive literature review for ${request.supplement}.

      Focus on:
      - Recent peer-reviewed studies (last 5 years)
      - Meta-analyses and systematic reviews
      - Mechanism of action studies
      - Efficacy evidence

      Research Goals: ${request.goals.join(', ')}

      For each finding, provide:
      1. Study type and quality
      2. Key findings
      3. Sample size and population
      4. Confidence level
      5. Clinical relevance

      Return structured findings as JSON array.
    `;

    const response = await this.aiService.chat(prompt, {
      model: request.model || 'gemma-3-4b-it-qat',
      temperature: 0.2
    });

    return this.parseResearchResults(response.message, 'literature');
  }

  /**
   * Execute clinical analysis using AI
   */
  private async executeClinicalAnalysis(request: EnhancedResearchRequest, plan: any): Promise<ResearchResult[]> {
    if (!request.includeClinicalTrials) return [];

    const prompt = `
      Analyze clinical trial data for ${request.supplement}.

      Focus on:
      - Randomized controlled trials (RCTs)
      - Clinical efficacy studies
      - Dosage optimization studies
      - Bioavailability research

      Research Goals: ${request.goals.join(', ')}

      Provide detailed analysis of:
      1. Trial design and methodology
      2. Primary and secondary endpoints
      3. Statistical significance
      4. Clinical significance
      5. Adverse events reported

      Return structured clinical findings as JSON array.
    `;

    const response = await this.aiService.chat(prompt, {
      model: request.model || 'gemma-3-4b-it-qat',
      temperature: 0.2
    });

    return this.parseResearchResults(response.message, 'clinical');
  }

  /**
   * Execute safety assessment using AI
   */
  private async executeSafetyAssessment(request: EnhancedResearchRequest, plan: any): Promise<ResearchResult[]> {
    const prompt = `
      Conduct comprehensive safety assessment for ${request.supplement}.

      Analyze:
      - Known side effects and adverse reactions
      - Contraindications and warnings
      - Special population considerations
      - Overdose potential and symptoms
      - Long-term safety data

      Research Goals: ${request.goals.join(', ')}

      Provide detailed safety profile including:
      1. Common side effects (frequency)
      2. Serious adverse events
      3. Drug interactions
      4. Special warnings
      5. Safe dosage ranges

      Return structured safety findings as JSON array.
    `;

    const response = await this.aiService.chat(prompt, {
      model: request.model || 'gemma-3-4b-it-qat',
      temperature: 0.1
    });

    return this.parseResearchResults(response.message, 'safety');
  }

  /**
   * Execute interaction analysis using AI
   */
  private async executeInteractionAnalysis(request: EnhancedResearchRequest, plan: any): Promise<ResearchResult[]> {
    const prompt = `
      Analyze potential interactions for ${request.supplement}.

      Investigate:
      - Drug-supplement interactions
      - Supplement-supplement interactions
      - Food-supplement interactions
      - Timing considerations

      Research Goals: ${request.goals.join(', ')}

      For each interaction, provide:
      1. Interaction mechanism
      2. Clinical significance
      3. Severity level
      4. Management recommendations
      5. Timing adjustments needed

      Return structured interaction findings as JSON array.
    `;

    const response = await this.aiService.chat(prompt, {
      model: request.model || 'gemma-3-4b-it-qat',
      temperature: 0.2
    });

    return this.parseResearchResults(response.message, 'interaction');
  }

  /**
   * Synthesize research results using AI
   */
  private async synthesizeResearchResults(results: ResearchResult[], request: EnhancedResearchRequest): Promise<ResearchResult[]> {
    const prompt = `
      Synthesize and analyze the following research findings for ${request.supplement}:

      ${JSON.stringify(results, null, 2)}

      Create a comprehensive synthesis that:
      1. Identifies key themes and patterns
      2. Resolves conflicting evidence
      3. Provides evidence-based conclusions
      4. Highlights research gaps
      5. Offers practical recommendations

      Return enhanced and synthesized findings as JSON array.
    `;

    const response = await this.aiService.chat(prompt, {
      model: request.model || 'gemma-3-4b-it-qat',
      temperature: 0.3
    });

    return this.parseResearchResults(response.message, 'synthesis');
  }

  /**
   * Create specialized agent team
   */
  private createAgentTeam(request: EnhancedResearchRequest): AgentStatus[] {
    const agents: AgentStatus[] = [
      {
        id: 'literature_specialist',
        name: 'Literature Review Specialist',
        status: 'idle',
        currentTask: 'Preparing to search scientific databases',
        progress: 0,
        lastUpdate: new Date(),
        metrics: { tasksCompleted: 0, averageTime: 0, successRate: 0 }
      },
      {
        id: 'clinical_analyst',
        name: 'Clinical Trial Analyst',
        status: 'idle',
        currentTask: 'Preparing clinical trial analysis',
        progress: 0,
        lastUpdate: new Date(),
        metrics: { tasksCompleted: 0, averageTime: 0, successRate: 0 }
      },
      {
        id: 'safety_assessor',
        name: 'Safety Assessment Specialist',
        status: 'idle',
        currentTask: 'Preparing safety profile analysis',
        progress: 0,
        lastUpdate: new Date(),
        metrics: { tasksCompleted: 0, averageTime: 0, successRate: 0 }
      }
    ];

    if (request.includeInteractions) {
      agents.push({
        id: 'interaction_mapper',
        name: 'Drug Interaction Specialist',
        status: 'idle',
        currentTask: 'Preparing interaction analysis',
        progress: 0,
        lastUpdate: new Date(),
        metrics: { tasksCompleted: 0, averageTime: 0, successRate: 0 }
      });
    }

    if (request.includeUserReviews) {
      agents.push({
        id: 'review_analyst',
        name: 'User Review Analyst',
        status: 'idle',
        currentTask: 'Preparing review sentiment analysis',
        progress: 0,
        lastUpdate: new Date(),
        metrics: { tasksCompleted: 0, averageTime: 0, successRate: 0 }
      });
    }

    return agents;
  }

  /**
   * Calculate total steps for research flow
   */
  private calculateTotalSteps(request: EnhancedResearchRequest): number {
    let steps = 4; // Base steps: planning, literature, clinical, safety

    if (request.includeInteractions) steps += 1;
    if (request.includeUserReviews) steps += 1;
    if (request.depth === 'comprehensive') steps += 1;
    if (request.depth === 'exhaustive') steps += 2;

    return steps;
  }

  /**
   * Update flow progress
   */
  private updateFlowProgress(flowId: string, progress: number, currentStep: string): void {
    const flow = this.activeFlows.get(flowId);
    if (flow) {
      flow.progress = progress;
      flow.currentStep = currentStep;
      flow.completedSteps = Math.floor((progress / 100) * flow.totalSteps);

      // Update agent progress
      flow.agents.forEach(agent => {
        if (agent.status === 'working') {
          agent.progress = Math.min(agent.progress + 10, 100);
          agent.lastUpdate = new Date();
        }
      });
    }
  }

  /**
   * Update flow status
   */
  private updateFlowStatus(flowId: string, status: ResearchFlow['status'], progress?: number): void {
    const flow = this.activeFlows.get(flowId);
    if (flow) {
      flow.status = status;
      if (progress !== undefined) {
        flow.progress = progress;
      }

      // Update all agents to completed if flow is completed
      if (status === 'completed') {
        flow.agents.forEach(agent => {
          agent.status = 'completed';
          agent.progress = 100;
          agent.lastUpdate = new Date();
        });
      }
    }
  }

  /**
   * Parse AI response to structured data
   */
  private parseAIResponse(response: string): any {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}|\[[\s\S]*\]/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      return { content: response };
    } catch (error) {
      logger.warn('Failed to parse AI response as JSON', { response: response.substring(0, 200) });
      return { content: response };
    }
  }

  /**
   * Parse research results from AI response
   */
  private parseResearchResults(response: string, type: string): ResearchResult[] {
    try {
      const parsed = this.parseAIResponse(response);

      if (Array.isArray(parsed)) {
        return parsed.map((item, index) => ({
          id: `${type}-${Date.now()}-${index}`,
          type: type as any,
          title: item.title || `${type} finding ${index + 1}`,
          content: item.content || item.description || JSON.stringify(item),
          confidence: item.confidence || 0.8,
          sources: item.sources || [],
          timestamp: new Date(),
          relevanceScore: item.relevanceScore || 0.8
        }));
      } else {
        return [{
          id: `${type}-${Date.now()}`,
          type: type as any,
          title: `${type} analysis`,
          content: parsed.content || response,
          confidence: 0.8,
          sources: [],
          timestamp: new Date(),
          relevanceScore: 0.8
        }];
      }
    } catch (error) {
      logger.warn('Failed to parse research results', { type, error });
      return [{
        id: `${type}-${Date.now()}`,
        type: type as any,
        title: `${type} analysis`,
        content: response,
        confidence: 0.7,
        sources: [],
        timestamp: new Date(),
        relevanceScore: 0.7
      }];
    }
  }

  /**
   * Get research results for a flow
   */
  async getResearchResults(flowId: string): Promise<ResearchResult[]> {
    const flow = this.activeFlows.get(flowId);
    return flow ? flow.results : [];
  }

  /**
   * Get active flows
   */
  async getActiveFlows(): Promise<ResearchFlow[]> {
    return Array.from(this.activeFlows.values());
  }

  /**
   * Stop research flow
   */
  async stopResearchFlow(flowId: string): Promise<void> {
    const flow = this.activeFlows.get(flowId);
    if (flow) {
      flow.status = 'error';
      flow.currentStep = 'Stopped by user';
      logger.info('Research flow stopped', { flowId });
    }
  }

  /**
   * Get flow status
   */
  async getFlowStatus(flowId: string): Promise<ResearchFlow | null> {
    return this.activeFlows.get(flowId) || null;
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      // Check MCP tools availability and LM Studio connection
      const models = await this.aiService.getAvailableModels();
      return models.lmStudio && models.lmStudio.length > 0;
    } catch (error) {
      logger.error('MCP Research Orchestrator health check failed:', error);
      return false;
    }
  }
}
