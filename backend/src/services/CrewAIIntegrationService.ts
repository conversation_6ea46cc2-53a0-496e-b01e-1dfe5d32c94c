import { spawn, ChildProcess } from 'child_process';
import { EventEmitter } from 'events';
import path from 'path';
import { logger } from '../utils/logger';

export interface CrewAIResearchRequest {
  supplementName: string;
  researchDepth: 'basic' | 'comprehensive' | 'exhaustive';
  researchGoals: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  sessionId?: string;
}

export interface CrewAIProgress {
  flowId: string;
  supplementName: string;
  currentStep: string;
  progressPercentage: number;
  completedTasks: string[];
  activeAgents: string[];
  estimatedCompletion?: Date;
  qualityScore?: number;
}

export interface CrewAIResult {
  flowId: string;
  supplementName: string;
  researchDepth: string;
  findings: Record<string, any>;
  qualityMetrics: Record<string, number>;
  executionTime: number;
  agentPerformance: Record<string, any>;
  timestamp: Date;
}

export interface AgentStatus {
  id: string;
  name: string;
  status: 'idle' | 'working' | 'completed' | 'error';
  currentTask?: string;
  progress: number;
  lastUpdate: Date;
  performance?: {
    executionTime: number;
    qualityScore: number;
    successRate: number;
  };
}

export class CrewAIIntegrationService extends EventEmitter {
  private pythonProcess: ChildProcess | null = null;
  private activeFlows: Map<string, CrewAIProgress> = new Map();
  private flowResults: Map<string, CrewAIResult> = new Map();
  private agentStatuses: Map<string, AgentStatus> = new Map();
  private isInitialized = false;

  constructor() {
    super();
    this.initializeService();
  }

  /**
   * Initialize the CrewAI integration service
   */
  private async initializeService(): Promise<void> {
    try {
      logger.info('Initializing CrewAI Integration Service...');
      
      // Initialize agent statuses
      this.initializeAgentStatuses();
      
      this.isInitialized = true;
      logger.info('CrewAI Integration Service initialized successfully');
      
    } catch (error) {
      logger.error('Failed to initialize CrewAI Integration Service:', error);
      throw error;
    }
  }

  /**
   * Initialize agent status tracking
   */
  private initializeAgentStatuses(): void {
    const agents = [
      { id: 'research_coordinator', name: 'Research Coordinator' },
      { id: 'literature_reviewer', name: 'Literature Review Specialist' },
      { id: 'clinical_analyst', name: 'Clinical Trial Analyst' },
      { id: 'interaction_mapper', name: 'Interaction Mapping Expert' },
      { id: 'safety_assessor', name: 'Safety Assessment Specialist' },
      { id: 'efficacy_evaluator', name: 'Efficacy Evaluation Specialist' },
      { id: 'market_analyst', name: 'Market Intelligence Analyst' },
      { id: 'regulatory_expert', name: 'Regulatory Compliance Expert' }
    ];

    agents.forEach(agent => {
      this.agentStatuses.set(agent.id, {
        id: agent.id,
        name: agent.name,
        status: 'idle',
        progress: 0,
        lastUpdate: new Date()
      });
    });
  }

  /**
   * Start a new research flow
   */
  async startResearch(request: CrewAIResearchRequest): Promise<string> {
    try {
      if (!this.isInitialized) {
        throw new Error('CrewAI Integration Service not initialized');
      }

      logger.info(`Starting CrewAI research for: ${request.supplementName}`);

      // Generate flow ID
      const flowId = `flow_${request.supplementName}_${Date.now()}`;

      // Create initial progress
      const initialProgress: CrewAIProgress = {
        flowId,
        supplementName: request.supplementName,
        currentStep: 'Initializing Research',
        progressPercentage: 0,
        completedTasks: [],
        activeAgents: ['research_coordinator'],
        estimatedCompletion: this.estimateCompletionTime(request.researchDepth)
      };

      this.activeFlows.set(flowId, initialProgress);

      // Start Python CrewAI process
      await this.startCrewAIProcess(flowId, request);

      // Emit initial progress
      this.emit('progress', initialProgress);

      return flowId;

    } catch (error) {
      logger.error('Failed to start CrewAI research:', error);
      throw error;
    }
  }

  /**
   * Start the Python CrewAI process
   */
  private async startCrewAIProcess(flowId: string, request: CrewAIResearchRequest): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const pythonScript = path.join(__dirname, '../crewai/flows/research_flow.py');
        const venvPython = path.join(__dirname, '../../venv/bin/python');

        // Prepare arguments
        const args = [
          pythonScript,
          '--supplement-name', request.supplementName,
          '--research-depth', request.researchDepth,
          '--research-goals', JSON.stringify(request.researchGoals),
          '--flow-id', flowId
        ];

        // Start Python process
        this.pythonProcess = spawn(venvPython, args, {
          stdio: ['pipe', 'pipe', 'pipe'],
          env: { ...process.env, PYTHONPATH: path.join(__dirname, '../crewai') }
        });

        // Handle stdout (progress updates)
        this.pythonProcess.stdout?.on('data', (data) => {
          this.handlePythonOutput(flowId, data.toString());
        });

        // Handle stderr (errors)
        this.pythonProcess.stderr?.on('data', (data) => {
          logger.error('CrewAI Python error:', data.toString());
        });

        // Handle process completion
        this.pythonProcess.on('close', (code) => {
          if (code === 0) {
            this.handleResearchCompletion(flowId);
            resolve();
          } else {
            reject(new Error(`CrewAI process exited with code ${code}`));
          }
        });

        // Handle process errors
        this.pythonProcess.on('error', (error) => {
          logger.error('CrewAI process error:', error);
          reject(error);
        });

        // Start progress simulation for demo
        this.simulateProgress(flowId, request);

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Handle output from Python CrewAI process
   */
  private handlePythonOutput(flowId: string, output: string): void {
    try {
      // Parse JSON output from Python
      const lines = output.trim().split('\n');
      
      for (const line of lines) {
        if (line.startsWith('{')) {
          try {
            const data = JSON.parse(line);
            
            if (data.type === 'progress') {
              this.updateProgress(flowId, data);
            } else if (data.type === 'agent_status') {
              this.updateAgentStatus(data);
            } else if (data.type === 'result') {
              this.handleResult(flowId, data);
            }
          } catch (parseError) {
            // Ignore non-JSON lines
          }
        }
      }
    } catch (error) {
      logger.error('Error handling Python output:', error);
    }
  }

  /**
   * Update progress for a flow
   */
  private updateProgress(flowId: string, data: any): void {
    const currentProgress = this.activeFlows.get(flowId);
    if (!currentProgress) return;

    const updatedProgress: CrewAIProgress = {
      ...currentProgress,
      currentStep: data.current_step || currentProgress.currentStep,
      progressPercentage: data.progress_percentage || currentProgress.progressPercentage,
      completedTasks: data.completed_tasks || currentProgress.completedTasks,
      activeAgents: data.active_agents || currentProgress.activeAgents,
      qualityScore: data.quality_score
    };

    this.activeFlows.set(flowId, updatedProgress);
    this.emit('progress', updatedProgress);
  }

  /**
   * Update agent status
   */
  private updateAgentStatus(data: any): void {
    const agentId = data.agent_id;
    const currentStatus = this.agentStatuses.get(agentId);
    
    if (currentStatus) {
      const updatedStatus: AgentStatus = {
        ...currentStatus,
        status: data.status || currentStatus.status,
        currentTask: data.current_task,
        progress: data.progress || currentStatus.progress,
        lastUpdate: new Date(),
        performance: data.performance
      };

      this.agentStatuses.set(agentId, updatedStatus);
      this.emit('agent_status', updatedStatus);
    }
  }

  /**
   * Handle research result
   */
  private handleResult(flowId: string, data: any): void {
    const result: CrewAIResult = {
      flowId,
      supplementName: data.supplement_name,
      researchDepth: data.research_depth,
      findings: data.findings || {},
      qualityMetrics: data.quality_metrics || {},
      executionTime: data.execution_time || 0,
      agentPerformance: data.agent_performance || {},
      timestamp: new Date()
    };

    this.flowResults.set(flowId, result);
    this.emit('result', result);
  }

  /**
   * Handle research completion
   */
  private handleResearchCompletion(flowId: string): void {
    const progress = this.activeFlows.get(flowId);
    if (progress) {
      const completedProgress: CrewAIProgress = {
        ...progress,
        currentStep: 'Research Completed',
        progressPercentage: 100,
        activeAgents: []
      };

      this.activeFlows.set(flowId, completedProgress);
      this.emit('progress', completedProgress);
      this.emit('completed', flowId);
    }
  }

  /**
   * Simulate progress for demonstration (remove when real integration is complete)
   */
  private simulateProgress(flowId: string, request: CrewAIResearchRequest): void {
    const steps = [
      { step: 'Research Planning', progress: 10, agents: ['research_coordinator'] },
      { step: 'Literature Review', progress: 25, agents: ['literature_reviewer'] },
      { step: 'Clinical Trial Analysis', progress: 45, agents: ['clinical_analyst'] },
      { step: 'Safety Assessment', progress: 65, agents: ['safety_assessor'] },
      { step: 'Efficacy Evaluation', progress: 80, agents: ['efficacy_evaluator'] },
      { step: 'Research Synthesis', progress: 95, agents: ['research_coordinator'] },
      { step: 'Quality Assessment', progress: 100, agents: [] }
    ];

    let stepIndex = 0;
    const interval = setInterval(() => {
      if (stepIndex >= steps.length) {
        clearInterval(interval);
        this.handleResearchCompletion(flowId);
        return;
      }

      const currentStep = steps[stepIndex];
      this.updateProgress(flowId, {
        current_step: currentStep.step,
        progress_percentage: currentStep.progress,
        active_agents: currentStep.agents,
        completed_tasks: steps.slice(0, stepIndex + 1).map(s => s.step.toLowerCase().replace(' ', '_'))
      });

      // Update agent statuses
      currentStep.agents.forEach(agentId => {
        this.updateAgentStatus({
          agent_id: agentId,
          status: 'working',
          current_task: `Executing ${currentStep.step}`,
          progress: Math.min(currentStep.progress + Math.random() * 10, 100)
        });
      });

      stepIndex++;
    }, 3000); // Update every 3 seconds
  }

  /**
   * Estimate completion time based on research depth
   */
  private estimateCompletionTime(researchDepth: string): Date {
    const minutes = {
      'basic': 5,
      'comprehensive': 10,
      'exhaustive': 15
    }[researchDepth] || 10;

    return new Date(Date.now() + minutes * 60 * 1000);
  }

  /**
   * Get progress for a specific flow
   */
  getFlowProgress(flowId: string): CrewAIProgress | undefined {
    return this.activeFlows.get(flowId);
  }

  /**
   * Get all active flows
   */
  getActiveFlows(): CrewAIProgress[] {
    return Array.from(this.activeFlows.values());
  }

  /**
   * Get result for a completed flow
   */
  getFlowResult(flowId: string): CrewAIResult | undefined {
    return this.flowResults.get(flowId);
  }

  /**
   * Get all agent statuses
   */
  getAgentStatuses(): AgentStatus[] {
    return Array.from(this.agentStatuses.values());
  }

  /**
   * Get specific agent status
   */
  getAgentStatus(agentId: string): AgentStatus | undefined {
    return this.agentStatuses.get(agentId);
  }

  /**
   * Stop a running research flow
   */
  async stopResearch(flowId: string): Promise<void> {
    try {
      if (this.pythonProcess) {
        this.pythonProcess.kill('SIGTERM');
        this.pythonProcess = null;
      }

      this.activeFlows.delete(flowId);
      this.emit('stopped', flowId);

      logger.info(`Stopped research flow: ${flowId}`);
    } catch (error) {
      logger.error('Error stopping research flow:', error);
      throw error;
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      return this.isInitialized;
    } catch (error) {
      logger.error('CrewAI Integration Service health check failed:', error);
      return false;
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    try {
      if (this.pythonProcess) {
        this.pythonProcess.kill('SIGTERM');
        this.pythonProcess = null;
      }

      this.activeFlows.clear();
      this.agentStatuses.clear();
      this.removeAllListeners();

      logger.info('CrewAI Integration Service cleaned up');
    } catch (error) {
      logger.error('Error during CrewAI cleanup:', error);
    }
  }
}

// Export singleton instance
export const crewAIService = new CrewAIIntegrationService();