#!/usr/bin/env node
/**
 * CrewAI Implementation Test Script
 * Tests the full CrewAI integration
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/crewai';

async function testCrewAIImplementation() {
  console.log('🚀 Testing CrewAI Full Implementation...\n');

  try {
    // Test 1: Health Check
    console.log('1. Testing Health Check...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health Check:', healthResponse.data.data.status);

    // Test 2: Get Capabilities
    console.log('\n2. Testing Capabilities...');
    const capabilitiesResponse = await axios.get(`${BASE_URL}/capabilities`);
    console.log('✅ Available Agents:', capabilitiesResponse.data.data.availableAgents.length);
    console.log('✅ Research Depths:', capabilitiesResponse.data.data.researchDepths);

    // Test 3: Start Research Flow
    console.log('\n3. Starting Research Flow...');
    const researchRequest = {
      supplementName: 'Vitamin D',
      researchDepth: 'comprehensive',
      researchGoals: ['safety_profile', 'efficacy_evidence', 'drug_interactions'],
      priority: 'high'
    };

    const startResponse = await axios.post(`${BASE_URL}/research/start`, researchRequest);
    const flowId = startResponse.data.data.flowId;
    console.log('✅ Research Started:', flowId);

    // Test 4: Monitor Progress
    console.log('\n4. Monitoring Progress...');
    let completed = false;
    let attempts = 0;
    const maxAttempts = 15;

    while (!completed && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      
      try {
        const progressResponse = await axios.get(`${BASE_URL}/research/${flowId}/progress`);
        const progress = progressResponse.data.data;
        
        console.log(`📊 Progress: ${progress.progressPercentage}% - ${progress.currentStep}`);
        console.log(`🤖 Active Agents: ${progress.activeAgents.join(', ')}`);
        
        if (progress.progressPercentage >= 100) {
          completed = true;
          console.log('✅ Research Completed!');
        }
      } catch (error) {
        console.log('⏳ Research still in progress...');
      }
      
      attempts++;
    }

    // Test 5: Get Results
    if (completed) {
      console.log('\n5. Getting Results...');
      try {
        const resultResponse = await axios.get(`${BASE_URL}/research/${flowId}/result`);
        const result = resultResponse.data.data;
        
        console.log('✅ Research Results:');
        console.log(`   Supplement: ${result.supplementName}`);
        console.log(`   Quality Score: ${(result.qualityMetrics.overall_score * 100).toFixed(1)}%`);
        console.log(`   Execution Time: ${result.executionTime}s`);
        console.log(`   Findings: ${Object.keys(result.findings).length} categories`);
      } catch (error) {
        console.log('⚠️ Results not yet available');
      }
    }

    // Test 6: Agent Status
    console.log('\n6. Checking Agent Status...');
    const agentStatusResponse = await axios.get(`${BASE_URL}/agents/status`);
    const agents = agentStatusResponse.data.data.agents;
    
    console.log('✅ Agent Statuses:');
    agents.forEach(agent => {
      console.log(`   ${agent.name}: ${agent.status} (${agent.progress}%)`);
    });

    // Test 7: Active Flows
    console.log('\n7. Checking Active Flows...');
    const activeFlowsResponse = await axios.get(`${BASE_URL}/research/active`);
    const activeFlows = activeFlowsResponse.data.data.flows;
    
    console.log(`✅ Active Flows: ${activeFlows.length}`);
    activeFlows.forEach(flow => {
      console.log(`   ${flow.supplementName}: ${flow.currentStep} (${flow.progressPercentage}%)`);
    });

    console.log('\n🎉 CrewAI Implementation Test Completed Successfully!');
    console.log('\n📊 Test Summary:');
    console.log('✅ Health Check: PASSED');
    console.log('✅ Capabilities: PASSED');
    console.log('✅ Research Flow: PASSED');
    console.log('✅ Progress Monitoring: PASSED');
    console.log('✅ Agent Status: PASSED');
    console.log('✅ Active Flows: PASSED');

  } catch (error) {
    console.error('❌ Test Failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

// Run the test
if (require.main === module) {
  testCrewAIImplementation();
}

module.exports = { testCrewAIImplementation };