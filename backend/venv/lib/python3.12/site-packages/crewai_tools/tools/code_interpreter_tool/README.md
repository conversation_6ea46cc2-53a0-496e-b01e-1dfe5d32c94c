# CodeInterpreterTool

## Description
This tool is used to give the Agent the ability to run code (Python3) from the code generated by the Agent itself. The code is executed in a sandboxed environment, so it is safe to run any code.

It is incredible useful since it allows the Agent to generate code, run it in the same environment, get the result and use it to make decisions.

## Requirements

- Docker

## Installation
Install the crewai_tools package
```shell
pip install 'crewai[tools]'
```

## Example

Remember that when using this tool, the code must be generated by the Agent itself. The code must be a Python3 code. And it will take some time for the first time to run because it needs to build the Docker image.

```python
from crewai_tools import CodeInterpreterTool

Agent(
    ...
    tools=[CodeInterpreterTool()],
)
```

Or if you need to pass your own Dockerfile just do this

```python
from crewai_tools import CodeInterpreterTool

Agent(
    ...
    tools=[CodeInterpreterTool(user_dockerfile_path="<Dockerfile_path>")],
)
```

If it is difficult to connect to docker daemon automatically (especially for macOS users), you can do this to setup docker host manually

```python 
from crewai_tools import CodeInterpreterTool

Agent(
    ...
    tools=[CodeInterpreterTool(user_docker_base_url="<Docker Host Base Url>",
        user_dockerfile_path="<Dockerfile_path>")],
)

```
