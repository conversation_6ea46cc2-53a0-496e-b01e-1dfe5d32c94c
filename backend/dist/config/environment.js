"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const joi_1 = __importDefault(require("joi"));
dotenv_1.default.config();
const envSchema = joi_1.default.object({
    NODE_ENV: joi_1.default.string()
        .valid('development', 'production', 'test')
        .default('development'),
    PORT: joi_1.default.number().default(3069),
    NEO4J_URI: joi_1.default.string().required(),
    NEO4J_USERNAME: joi_1.default.string().required(),
    NEO4J_PASSWORD: joi_1.default.string().required(),
    WEAVIATE_URL: joi_1.default.string().uri().required(),
    WEAVIATE_API_KEY: joi_1.default.string().optional(),
    REDIS_URL: joi_1.default.string().required(),
    REDIS_PASSWORD: joi_1.default.string().optional(),
    MONGODB_URI: joi_1.default.string().required(),
    GEMINI_API_KEY: joi_1.default.string().required(),
    OPENAI_API_KEY: joi_1.default.string().optional(),
    ANTHROPIC_API_KEY: joi_1.default.string().optional(),
    LM_STUDIO_URL: joi_1.default.string().uri().optional(),
    LM_STUDIO_API_KEY: joi_1.default.string().optional(),
    OLLAMA_URL: joi_1.default.string().uri().optional(),
    BRAVE_API_KEY: joi_1.default.string().optional(),
    TAVILY_API_KEY: joi_1.default.string().optional(),
    GEMMA_MEDICAL_URL: joi_1.default.string().uri().default('http://*************:1234'),
    JWT_SECRET: joi_1.default.string().min(32).default('your-super-secret-jwt-key-change-this-in-production'),
    JWT_EXPIRES_IN: joi_1.default.string().default('7d'),
    CORS_ORIGINS: joi_1.default.string().default('http://localhost:5174,http://localhost:3069'),
    MAX_FILE_SIZE: joi_1.default.number().default(50 * 1024 * 1024),
    UPLOAD_DIR: joi_1.default.string().default('./uploads'),
    RATE_LIMIT_WINDOW_MS: joi_1.default.number().default(15 * 60 * 1000),
    RATE_LIMIT_MAX_REQUESTS: joi_1.default.number().default(100),
    LOG_LEVEL: joi_1.default.string()
        .valid('error', 'warn', 'info', 'debug')
        .default('info'),
    LOG_FILE: joi_1.default.string().default('./logs/app.log'),
    CACHE_TTL: joi_1.default.number().default(3600),
    DEFAULT_AI_MODEL: joi_1.default.string().default('gemini-pro'),
    MAX_TOKENS: joi_1.default.number().default(4096),
    TEMPERATURE: joi_1.default.number().min(0).max(2).default(0.7),
    MAX_GRAPH_NODES: joi_1.default.number().default(10000),
    MAX_GRAPH_RELATIONSHIPS: joi_1.default.number().default(50000),
    GRAPH_EXPANSION_LIMIT: joi_1.default.number().default(100),
    EMBEDDING_MODEL: joi_1.default.string().default('text-embedding-ada-002'),
    VECTOR_DIMENSION: joi_1.default.number().default(1536),
    SIMILARITY_THRESHOLD: joi_1.default.number().min(0).max(1).default(0.7),
    MAX_CONTEXT_LENGTH: joi_1.default.number().default(8000),
    CRAWL_DELAY_MS: joi_1.default.number().default(1000),
    MAX_CRAWL_DEPTH: joi_1.default.number().default(3),
    MAX_PAGES_PER_DOMAIN: joi_1.default.number().default(100),
    HEALTH_CHECK_INTERVAL: joi_1.default.number().default(30000),
}).unknown();
const { error, value: envVars } = envSchema.validate(process.env);
if (error) {
    throw new Error(`Config validation error: ${error.message}`);
}
exports.config = {
    nodeEnv: envVars.NODE_ENV,
    isDevelopment: envVars.NODE_ENV === 'development',
    isProduction: envVars.NODE_ENV === 'production',
    isTest: envVars.NODE_ENV === 'test',
    port: envVars.PORT,
    neo4j: {
        uri: envVars.NEO4J_URI,
        username: envVars.NEO4J_USERNAME,
        password: envVars.NEO4J_PASSWORD,
    },
    weaviate: {
        url: envVars.WEAVIATE_URL,
        apiKey: envVars.WEAVIATE_API_KEY,
    },
    redis: {
        url: envVars.REDIS_URL,
        password: envVars.REDIS_PASSWORD,
    },
    mongodb: {
        uri: envVars.MONGODB_URI,
    },
    ai: {
        geminiApiKey: envVars.GEMINI_API_KEY,
        openaiApiKey: envVars.OPENAI_API_KEY,
        anthropicApiKey: envVars.ANTHROPIC_API_KEY,
        lmStudioUrl: envVars.LM_STUDIO_URL,
        lmStudioApiKey: envVars.LM_STUDIO_API_KEY,
        ollamaUrl: envVars.OLLAMA_URL,
        defaultModel: envVars.DEFAULT_AI_MODEL,
        maxTokens: envVars.MAX_TOKENS,
        temperature: envVars.TEMPERATURE,
    },
    research: {
        braveApiKey: envVars.BRAVE_API_KEY,
        tavilyApiKey: envVars.TAVILY_API_KEY,
        gemmaMedicalUrl: envVars.GEMMA_MEDICAL_URL,
    },
    jwt: {
        secret: envVars.JWT_SECRET,
        expiresIn: envVars.JWT_EXPIRES_IN,
    },
    corsOrigins: envVars.CORS_ORIGINS.split(',').map(origin => origin.trim()),
    upload: {
        maxFileSize: envVars.MAX_FILE_SIZE,
        uploadDir: envVars.UPLOAD_DIR,
    },
    rateLimit: {
        windowMs: envVars.RATE_LIMIT_WINDOW_MS,
        maxRequests: envVars.RATE_LIMIT_MAX_REQUESTS,
    },
    logging: {
        level: envVars.LOG_LEVEL,
        file: envVars.LOG_FILE,
    },
    cache: {
        ttl: envVars.CACHE_TTL,
    },
    graph: {
        maxNodes: envVars.MAX_GRAPH_NODES,
        maxRelationships: envVars.MAX_GRAPH_RELATIONSHIPS,
        expansionLimit: envVars.GRAPH_EXPANSION_LIMIT,
    },
    rag: {
        embeddingModel: envVars.EMBEDDING_MODEL,
        vectorDimension: envVars.VECTOR_DIMENSION,
        similarityThreshold: envVars.SIMILARITY_THRESHOLD,
        maxContextLength: envVars.MAX_CONTEXT_LENGTH,
    },
    crawling: {
        delayMs: envVars.CRAWL_DELAY_MS,
        maxDepth: envVars.MAX_CRAWL_DEPTH,
        maxPagesPerDomain: envVars.MAX_PAGES_PER_DOMAIN,
    },
    healthCheck: {
        interval: envVars.HEALTH_CHECK_INTERVAL,
    },
};
//# sourceMappingURL=environment.js.map