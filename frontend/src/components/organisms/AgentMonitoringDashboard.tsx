import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Badge from '@/components/atoms/Badge';
import Button from '@/components/atoms/Button';
import { useEnhancedResearch } from '@/hooks/useEnhancedResearch';
import { agenticAPI } from '@/services/api';
import {
  Bot,
  Activity,
  Clock,
  CheckCircle,
  AlertTriangle,
  Zap,
  Brain,
  Search,
  Database,
  TrendingUp,
  Pause,
  Play,
  Square,
  RefreshCw
} from 'lucide-react';

interface SystemMetrics {
  totalAgents: number;
  activeAgents: number;
  completedTasks: number;
  averageResponseTime: number;
  successRate: number;
  memoryUsage: number;
  cpuUsage: number;
}

interface AgentPerformance {
  agentId: string;
  name: string;
  tasksCompleted: number;
  averageTime: number;
  successRate: number;
  currentLoad: number;
  status: 'idle' | 'working' | 'overloaded' | 'error';
}

const AgentMonitoringDashboard: React.FC = () => {
  const {
    agents,
    activeFlows,
    currentFlow,
    isResearching,
    error,
    stopResearch
  } = useEnhancedResearch();

  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics>({
    totalAgents: 0,
    activeAgents: 0,
    completedTasks: 0,
    averageResponseTime: 0,
    successRate: 0,
    memoryUsage: 0,
    cpuUsage: 0
  });

  const [agentPerformance, setAgentPerformance] = useState<AgentPerformance[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(5000);

  // Fetch system metrics
  const fetchSystemMetrics = async () => {
    try {
      const metrics = await agenticAPI.monitoring.getSystemStatus();
      setSystemMetrics(metrics);
    } catch (error) {
      console.error('Failed to fetch system metrics:', error);
    }
  };

  // Fetch agent performance data
  const fetchAgentPerformance = async () => {
    try {
      const performance = await agenticAPI.monitoring.getAgentMetrics();
      setAgentPerformance(performance);
    } catch (error) {
      console.error('Failed to fetch agent performance:', error);
    }
  };

  // Auto-refresh monitoring data
  useEffect(() => {
    if (isMonitoring) {
      const interval = setInterval(() => {
        fetchSystemMetrics();
        fetchAgentPerformance();
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [isMonitoring, refreshInterval]);

  // Initial data fetch
  useEffect(() => {
    fetchSystemMetrics();
    fetchAgentPerformance();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'working': return 'bg-blue-100 text-blue-800';
      case 'idle': return 'bg-gray-100 text-gray-800';
      case 'overloaded': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'completed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'working': return <Activity className="w-4 h-4" />;
      case 'idle': return <Clock className="w-4 h-4" />;
      case 'overloaded': return <AlertTriangle className="w-4 h-4" />;
      case 'error': return <AlertTriangle className="w-4 h-4" />;
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      default: return <Bot className="w-4 h-4" />;
    }
  };

  const getAgentTypeIcon = (agentId: string) => {
    if (agentId.includes('literature')) return <Search className="w-5 h-5 text-blue-600" />;
    if (agentId.includes('clinical')) return <Database className="w-5 h-5 text-green-600" />;
    if (agentId.includes('interaction')) return <Zap className="w-5 h-5 text-yellow-600" />;
    if (agentId.includes('safety')) return <AlertTriangle className="w-5 h-5 text-red-600" />;
    if (agentId.includes('review')) return <TrendingUp className="w-5 h-5 text-purple-600" />;
    return <Brain className="w-5 h-5 text-gray-600" />;
  };

  return (
    <div className="space-y-6">
      {/* System Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-blue-600" />
              System Overview
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setIsMonitoring(!isMonitoring)}
                className="flex items-center gap-1"
              >
                {isMonitoring ? <Pause className="w-3 h-3" /> : <Play className="w-3 h-3" />}
                {isMonitoring ? 'Pause' : 'Resume'}
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  fetchSystemMetrics();
                  fetchAgentPerformance();
                }}
                className="flex items-center gap-1"
              >
                <RefreshCw className="w-3 h-3" />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
            <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-3">
              <div className="text-sm text-blue-600 font-medium">Total Agents</div>
              <div className="text-2xl font-bold text-blue-700">{systemMetrics.totalAgents}</div>
            </div>
            <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-3">
              <div className="text-sm text-green-600 font-medium">Active</div>
              <div className="text-2xl font-bold text-green-700">{systemMetrics.activeAgents}</div>
            </div>
            <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-3">
              <div className="text-sm text-purple-600 font-medium">Tasks Done</div>
              <div className="text-2xl font-bold text-purple-700">{systemMetrics.completedTasks}</div>
            </div>
            <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-3">
              <div className="text-sm text-yellow-600 font-medium">Avg Time</div>
              <div className="text-2xl font-bold text-yellow-700">{systemMetrics.averageResponseTime}s</div>
            </div>
            <div className="bg-gradient-to-r from-emerald-50 to-emerald-100 rounded-lg p-3">
              <div className="text-sm text-emerald-600 font-medium">Success Rate</div>
              <div className="text-2xl font-bold text-emerald-700">{systemMetrics.successRate}%</div>
            </div>
            <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-3">
              <div className="text-sm text-orange-600 font-medium">Memory</div>
              <div className="text-2xl font-bold text-orange-700">{systemMetrics.memoryUsage}%</div>
            </div>
            <div className="bg-gradient-to-r from-red-50 to-red-100 rounded-lg p-3">
              <div className="text-sm text-red-600 font-medium">CPU</div>
              <div className="text-2xl font-bold text-red-700">{systemMetrics.cpuUsage}%</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Active Research Flows */}
      {activeFlows.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="w-5 h-5 text-purple-600" />
              Active Research Flows ({activeFlows.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {activeFlows.map(flow => (
                <div key={flow.id} className="border rounded-lg p-4 bg-gradient-to-r from-white to-purple-50">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="font-semibold">{flow.name}</h4>
                      <p className="text-sm text-gray-600">{flow.currentStep}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(flow.status)}>
                        {getStatusIcon(flow.status)}
                        {flow.status}
                      </Badge>
                      {flow.status === 'executing' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => stopResearch(flow.id)}
                          className="flex items-center gap-1"
                        >
                          <Square className="w-3 h-3" />
                          Stop
                        </Button>
                      )}
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div
                      className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${flow.progress}%` }}
                    />
                  </div>
                  <div className="text-sm text-gray-600">
                    Progress: {flow.progress}% • Step {flow.completedSteps + 1} of {flow.totalSteps}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Agent Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5 text-green-600" />
            Agent Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {agents.map(agent => (
              <div key={agent.id} className="border rounded-lg p-4 bg-gradient-to-br from-white to-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    {getAgentTypeIcon(agent.id)}
                    <h4 className="font-medium text-sm">{agent.name}</h4>
                  </div>
                  <Badge className={getStatusColor(agent.status)}>
                    {getStatusIcon(agent.status)}
                    {agent.status}
                  </Badge>
                </div>
                
                {agent.currentTask && (
                  <p className="text-xs text-gray-600 mb-3">{agent.currentTask}</p>
                )}
                
                <div className="space-y-2">
                  <div className="flex justify-between text-xs">
                    <span>Progress</span>
                    <span>{agent.progress.toFixed(0)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${agent.progress}%` }}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 mt-3">
                    <div>Tasks: {agent.metrics.tasksCompleted}</div>
                    <div>Avg: {agent.metrics.averageTime}s</div>
                    <div>Success: {agent.metrics.successRate}%</div>
                    <div>Updated: {agent.lastUpdate.toLocaleTimeString()}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-800">
              <AlertTriangle className="w-5 h-5" />
              System Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700">{error}</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AgentMonitoringDashboard;
