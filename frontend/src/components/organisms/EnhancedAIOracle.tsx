import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import Badge from '@/components/atoms/Badge';
import Button from '@/components/atoms/Button';
import { agenticAPI } from '@/services/api';
import {
  Brain,
  Sparkles,
  TrendingUp,
  Alert<PERSON>riangle,
  CheckCircle,
  Heart,
  Shield,
  Zap,
  Clock,
  Target,
  Activity,
  Lightbulb,
  Star,
  ArrowRight
} from 'lucide-react';

interface Prediction {
  id: string;
  type: 'health_outcome' | 'interaction_risk' | 'efficacy' | 'optimization';
  title: string;
  description: string;
  confidence: number;
  impact: 'low' | 'medium' | 'high' | 'critical';
  timeframe: string;
  evidence: string[];
  actionable: boolean;
  recommendation?: string;
}

interface HealthInsight {
  id: string;
  category: 'nutrition' | 'supplementation' | 'lifestyle' | 'monitoring';
  title: string;
  insight: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  personalized: boolean;
  sources: number;
  lastUpdated: Date;
}

interface UserProfile {
  age: number;
  gender: string;
  healthGoals: string[];
  currentSupplements: string[];
  conditions: string[];
  lifestyle: {
    activity_level: string;
    diet_type: string;
    sleep_hours: number;
    stress_level: string;
  };
}

const EnhancedAIOracle: React.FC = () => {
  const [predictions, setPredictions] = useState<Prediction[]>([]);
  const [insights, setInsights] = useState<HealthInsight[]>([]);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedPrediction, setSelectedPrediction] = useState<Prediction | null>(null);
  const [oracleMode, setOracleMode] = useState<'predictions' | 'insights' | 'recommendations'>('predictions');

  // Generate AI predictions
  const generatePredictions = async () => {
    if (!userProfile) return;
    
    setIsGenerating(true);
    try {
      const response = await agenticAPI.oracle.generatePredictions(
        userProfile,
        userProfile.currentSupplements
      );
      setPredictions(response.predictions || []);
    } catch (error) {
      console.error('Failed to generate predictions:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Get health analysis
  const getHealthAnalysis = async () => {
    if (!userProfile) return;
    
    setIsGenerating(true);
    try {
      const response = await agenticAPI.oracle.analyzeHealth({
        profile: userProfile,
        supplements: userProfile.currentSupplements,
        timestamp: new Date().toISOString()
      });
      setInsights(response.insights || []);
    } catch (error) {
      console.error('Failed to get health analysis:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Get personalized recommendations
  const getRecommendations = async () => {
    if (!userProfile) return;
    
    setIsGenerating(true);
    try {
      const response = await agenticAPI.oracle.getRecommendations('current-user', {
        profile: userProfile,
        focus: 'optimization'
      });
      // Process recommendations into insights format
      const recommendationInsights = response.recommendations?.map((rec: any, index: number) => ({
        id: `rec-${index}`,
        category: 'supplementation' as const,
        title: rec.title,
        insight: rec.description,
        priority: rec.priority,
        personalized: true,
        sources: rec.evidence?.length || 0,
        lastUpdated: new Date()
      })) || [];
      setInsights(recommendationInsights);
    } catch (error) {
      console.error('Failed to get recommendations:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Initialize with mock user profile
  useEffect(() => {
    const mockProfile: UserProfile = {
      age: 35,
      gender: 'male',
      healthGoals: ['Energy Optimization', 'Cognitive Enhancement', 'Immune Support'],
      currentSupplements: ['Vitamin D', 'Omega-3', 'Magnesium', 'B-Complex'],
      conditions: ['Mild Anxiety', 'Occasional Insomnia'],
      lifestyle: {
        activity_level: 'moderate',
        diet_type: 'balanced',
        sleep_hours: 7,
        stress_level: 'medium'
      }
    };
    setUserProfile(mockProfile);
  }, []);

  // Auto-generate predictions when profile is loaded
  useEffect(() => {
    if (userProfile && oracleMode === 'predictions') {
      generatePredictions();
    } else if (userProfile && oracleMode === 'insights') {
      getHealthAnalysis();
    } else if (userProfile && oracleMode === 'recommendations') {
      getRecommendations();
    }
  }, [userProfile, oracleMode]);

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getImpactIcon = (impact: string) => {
    switch (impact) {
      case 'critical': return <AlertTriangle className="w-4 h-4" />;
      case 'high': return <TrendingUp className="w-4 h-4" />;
      case 'medium': return <Activity className="w-4 h-4" />;
      case 'low': return <CheckCircle className="w-4 h-4" />;
      default: return <Target className="w-4 h-4" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'nutrition': return <Heart className="w-4 h-4" />;
      case 'supplementation': return <Zap className="w-4 h-4" />;
      case 'lifestyle': return <Activity className="w-4 h-4" />;
      case 'monitoring': return <Shield className="w-4 h-4" />;
      default: return <Lightbulb className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Oracle Header */}
      <Card className="bg-gradient-to-r from-purple-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-6 h-6 text-purple-600" />
            Enhanced AI Oracle
            <Sparkles className="w-5 h-5 text-yellow-500" />
          </CardTitle>
          <p className="text-gray-600">
            Advanced AI-powered health insights, predictions, and personalized recommendations
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4">
            <Button
              variant={oracleMode === 'predictions' ? 'primary' : 'outline'}
              onClick={() => setOracleMode('predictions')}
              className="flex items-center gap-2"
            >
              <TrendingUp className="w-4 h-4" />
              Predictions
            </Button>
            <Button
              variant={oracleMode === 'insights' ? 'primary' : 'outline'}
              onClick={() => setOracleMode('insights')}
              className="flex items-center gap-2"
            >
              <Lightbulb className="w-4 h-4" />
              Insights
            </Button>
            <Button
              variant={oracleMode === 'recommendations' ? 'primary' : 'outline'}
              onClick={() => setOracleMode('recommendations')}
              className="flex items-center gap-2"
            >
              <Star className="w-4 h-4" />
              Recommendations
            </Button>
          </div>

          {userProfile && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-white rounded-lg border">
              <div>
                <div className="text-sm text-gray-600">Health Goals</div>
                <div className="font-medium">{userProfile.healthGoals.length} active</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Supplements</div>
                <div className="font-medium">{userProfile.currentSupplements.length} current</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Activity Level</div>
                <div className="font-medium capitalize">{userProfile.lifestyle.activity_level}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Sleep</div>
                <div className="font-medium">{userProfile.lifestyle.sleep_hours}h/night</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Predictions Mode */}
      {oracleMode === 'predictions' && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-blue-600" />
                AI Health Predictions
              </CardTitle>
              <Button
                onClick={generatePredictions}
                disabled={isGenerating}
                className="flex items-center gap-2"
              >
                <Brain className="w-4 h-4" />
                {isGenerating ? 'Generating...' : 'Regenerate'}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {predictions.map(prediction => (
                <div
                  key={prediction.id}
                  className="border rounded-lg p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => setSelectedPrediction(prediction)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold">{prediction.title}</h4>
                    <div className="flex items-center gap-2">
                      <Badge className={getImpactColor(prediction.impact)}>
                        {getImpactIcon(prediction.impact)}
                        {prediction.impact}
                      </Badge>
                      <Badge className="bg-blue-100 text-blue-800">
                        {(prediction.confidence * 100).toFixed(0)}% confidence
                      </Badge>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-2">{prediction.description}</p>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center gap-2">
                      <Clock className="w-3 h-3" />
                      {prediction.timeframe}
                    </div>
                    {prediction.actionable && (
                      <div className="flex items-center gap-1 text-green-600">
                        <CheckCircle className="w-3 h-3" />
                        Actionable
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Insights Mode */}
      {(oracleMode === 'insights' || oracleMode === 'recommendations') && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                {oracleMode === 'insights' ? (
                  <>
                    <Lightbulb className="w-5 h-5 text-yellow-600" />
                    Health Insights
                  </>
                ) : (
                  <>
                    <Star className="w-5 h-5 text-purple-600" />
                    Personalized Recommendations
                  </>
                )}
              </CardTitle>
              <Button
                onClick={oracleMode === 'insights' ? getHealthAnalysis : getRecommendations}
                disabled={isGenerating}
                className="flex items-center gap-2"
              >
                <Brain className="w-4 h-4" />
                {isGenerating ? 'Analyzing...' : 'Refresh'}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {insights.map(insight => (
                <div key={insight.id} className="border rounded-lg p-4 bg-gradient-to-br from-white to-gray-50">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getCategoryIcon(insight.category)}
                      <h4 className="font-semibold">{insight.title}</h4>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getPriorityColor(insight.priority)}>
                        {insight.priority}
                      </Badge>
                      {insight.personalized && (
                        <Badge className="bg-purple-100 text-purple-800">
                          <Star className="w-3 h-3 mr-1" />
                          Personal
                        </Badge>
                      )}
                    </div>
                  </div>
                  <p className="text-gray-700 mb-3">{insight.insight}</p>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div>Sources: {insight.sources}</div>
                    <div>{insight.lastUpdated.toLocaleDateString()}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Selected Prediction Details */}
      {selectedPrediction && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5 text-blue-600" />
                Prediction Details
              </CardTitle>
              <Button
                variant="outline"
                onClick={() => setSelectedPrediction(null)}
              >
                Close
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">{selectedPrediction.title}</h4>
                <p className="text-gray-700">{selectedPrediction.description}</p>
              </div>
              
              {selectedPrediction.recommendation && (
                <div className="bg-white rounded-lg p-4 border">
                  <h5 className="font-medium mb-2 flex items-center gap-2">
                    <ArrowRight className="w-4 h-4 text-green-600" />
                    Recommended Action
                  </h5>
                  <p className="text-gray-700">{selectedPrediction.recommendation}</p>
                </div>
              )}
              
              <div>
                <h5 className="font-medium mb-2">Supporting Evidence</h5>
                <ul className="space-y-1">
                  {selectedPrediction.evidence.map((evidence, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                      <CheckCircle className="w-3 h-3 mt-0.5 text-green-600" />
                      {evidence}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EnhancedAIOracle;
