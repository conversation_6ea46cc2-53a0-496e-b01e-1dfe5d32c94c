import { agenticAPI, agenticWS } from './api';

// Enhanced Research Interfaces
export interface ResearchQuery {
  supplement: string;
  goals: string[];
  depth: 'basic' | 'comprehensive' | 'exhaustive';
  includeInteractions: boolean;
  includeClinicalTrials: boolean;
  includeUserReviews: boolean;
  userId?: string;
}

export interface ResearchResult {
  id: string;
  type: 'literature' | 'clinical' | 'safety' | 'interaction' | 'user_review';
  title: string;
  content: string;
  confidence: number;
  sources: string[];
  timestamp: Date;
  relevanceScore: number;
}

export interface AgentStatus {
  id: string;
  name: string;
  status: 'idle' | 'working' | 'completed' | 'error';
  currentTask?: string;
  progress: number;
  lastUpdate: Date;
  metrics: {
    tasksCompleted: number;
    averageTime: number;
    successRate: number;
  };
}

export interface ResearchFlow {
  id: string;
  name: string;
  status: 'planning' | 'executing' | 'completed' | 'error';
  progress: number;
  startTime: Date;
  estimatedCompletion?: Date;
  agents: AgentStatus[];
  currentStep: string;
  totalSteps: number;
  completedSteps: number;
  results: ResearchResult[];
}

// Enhanced Research Service Class
export class EnhancedResearchService {
  private activeFlows: Map<string, ResearchFlow> = new Map();
  private eventListeners: Map<string, Set<Function>> = new Map();

  constructor() {
    this.initializeWebSocket();
  }

  private initializeWebSocket() {
    // Subscribe to research events
    agenticWS.subscribe('research_update', this.handleResearchUpdate.bind(this));
    agenticWS.subscribe('agent_status', this.handleAgentStatus.bind(this));
    agenticWS.subscribe('flow_completed', this.handleFlowCompleted.bind(this));
  }

  private handleResearchUpdate(data: any) {
    const { flowId, update } = data;
    const flow = this.activeFlows.get(flowId);
    if (flow) {
      Object.assign(flow, update);
      this.emit('flow_updated', flow);
    }
  }

  private handleAgentStatus(data: any) {
    const { flowId, agentId, status } = data;
    const flow = this.activeFlows.get(flowId);
    if (flow) {
      const agent = flow.agents.find(a => a.id === agentId);
      if (agent) {
        Object.assign(agent, status);
        this.emit('agent_updated', { flowId, agent });
      }
    }
  }

  private handleFlowCompleted(data: any) {
    const { flowId, results } = data;
    const flow = this.activeFlows.get(flowId);
    if (flow) {
      flow.status = 'completed';
      flow.results = results;
      this.emit('flow_completed', flow);
    }
  }

  // Start autonomous research
  async startAutonomousResearch(query: ResearchQuery): Promise<string> {
    try {
      const response = await agenticAPI.research.startAutonomous(query);
      const flowId = response.flowId;
      
      // Create local flow tracking
      const flow: ResearchFlow = {
        id: flowId,
        name: `Research: ${query.supplement}`,
        status: 'planning',
        progress: 0,
        startTime: new Date(),
        agents: this.createAgentTeam(query),
        currentStep: 'Initializing research agents',
        totalSteps: this.calculateTotalSteps(query),
        completedSteps: 0,
        results: []
      };

      this.activeFlows.set(flowId, flow);
      this.emit('flow_started', flow);

      return flowId;
    } catch (error) {
      console.error('Failed to start autonomous research:', error);
      throw error;
    }
  }

  // Create specialized agent team based on research query
  private createAgentTeam(query: ResearchQuery): AgentStatus[] {
    const agents: AgentStatus[] = [
      {
        id: 'literature_specialist',
        name: 'Literature Review Specialist',
        status: 'idle',
        currentTask: 'Preparing to search scientific databases',
        progress: 0,
        lastUpdate: new Date(),
        metrics: { tasksCompleted: 0, averageTime: 0, successRate: 0 }
      },
      {
        id: 'clinical_analyst',
        name: 'Clinical Trial Analyst',
        status: 'idle',
        currentTask: 'Preparing clinical trial analysis',
        progress: 0,
        lastUpdate: new Date(),
        metrics: { tasksCompleted: 0, averageTime: 0, successRate: 0 }
      }
    ];

    if (query.includeInteractions) {
      agents.push({
        id: 'interaction_mapper',
        name: 'Drug Interaction Specialist',
        status: 'idle',
        currentTask: 'Preparing interaction analysis',
        progress: 0,
        lastUpdate: new Date(),
        metrics: { tasksCompleted: 0, averageTime: 0, successRate: 0 }
      });
    }

    if (query.includeUserReviews) {
      agents.push({
        id: 'review_analyst',
        name: 'User Review Analyst',
        status: 'idle',
        currentTask: 'Preparing review sentiment analysis',
        progress: 0,
        lastUpdate: new Date(),
        metrics: { tasksCompleted: 0, averageTime: 0, successRate: 0 }
      });
    }

    agents.push({
      id: 'safety_assessor',
      name: 'Safety Assessment Specialist',
      status: 'idle',
      currentTask: 'Preparing safety profile analysis',
      progress: 0,
      lastUpdate: new Date(),
      metrics: { tasksCompleted: 0, averageTime: 0, successRate: 0 }
    });

    return agents;
  }

  private calculateTotalSteps(query: ResearchQuery): number {
    let steps = 3; // Base steps: planning, literature review, safety assessment
    
    if (query.includeClinicalTrials) steps += 1;
    if (query.includeInteractions) steps += 1;
    if (query.includeUserReviews) steps += 1;
    if (query.depth === 'comprehensive') steps += 1;
    if (query.depth === 'exhaustive') steps += 2;
    
    return steps;
  }

  // Get research results
  async getResearchResults(flowId: string): Promise<ResearchResult[]> {
    try {
      const results = await agenticAPI.research.getResults(flowId);
      return results;
    } catch (error) {
      console.error('Failed to get research results:', error);
      throw error;
    }
  }

  // Search supplements with enhanced capabilities
  async searchSupplements(query: string, options: any = {}): Promise<any> {
    try {
      const results = await agenticAPI.research.searchSupplements(query, {
        includeTavily: true,
        includeBraveSearch: true,
        includeScientificDatabases: true,
        ...options
      });
      return results;
    } catch (error) {
      console.error('Failed to search supplements:', error);
      throw error;
    }
  }

  // Analyze supplement interactions
  async analyzeInteractions(supplements: string[]): Promise<any> {
    try {
      const analysis = await agenticAPI.research.analyzeInteractions(supplements);
      return analysis;
    } catch (error) {
      console.error('Failed to analyze interactions:', error);
      throw error;
    }
  }

  // Get personalized insights
  async getPersonalizedInsights(userId: string, supplements?: string[]): Promise<any> {
    try {
      const insights = await agenticAPI.research.getInsights(userId, supplements);
      return insights;
    } catch (error) {
      console.error('Failed to get personalized insights:', error);
      throw error;
    }
  }

  // Event system
  on(event: string, callback: Function) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  off(event: string, callback: Function) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  private emit(event: string, data: any) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }

  // Get active flows
  getActiveFlows(): ResearchFlow[] {
    return Array.from(this.activeFlows.values());
  }

  // Get flow by ID
  getFlow(flowId: string): ResearchFlow | undefined {
    return this.activeFlows.get(flowId);
  }

  // Stop research flow
  async stopFlow(flowId: string): Promise<void> {
    try {
      await agenticAPI.flows.stop(flowId);
      const flow = this.activeFlows.get(flowId);
      if (flow) {
        flow.status = 'error';
        this.emit('flow_stopped', flow);
      }
    } catch (error) {
      console.error('Failed to stop flow:', error);
      throw error;
    }
  }
}

// Global enhanced research service instance
export const enhancedResearchService = new EnhancedResearchService();
