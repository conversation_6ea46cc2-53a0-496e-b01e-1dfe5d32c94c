import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  SparklesIcon,
  BeakerIcon,
  BoltIcon,
  MagnifyingGlassIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  LightBulbIcon,
  UserIcon,
  CpuChipIcon,
  GlobeAltIcon,
  DevicePhoneMobileIcon,
} from '@heroicons/react/24/outline';

// Import all cosmic components
import CosmicSupplementSearch from '../components/search/CosmicSupplementSearch';
import CosmicStackBuilder from '../components/stack/CosmicStackBuilder';
import CosmicKnowledgeGraph from '../components/graph/CosmicKnowledgeGraph';
import CosmicDashboard from '../components/dashboard/CosmicDashboard';
import HealthProfileManager from '../components/profile/HealthProfileManager';

// Import new cosmic power components
import CosmicAIOracle from '../components/ai/CosmicAIOracle';
import ResearchSingularityEngine from '../components/research/ResearchSingularityEngine';
import QuantumSecurityCenter from '../components/security/QuantumSecurityCenter';
import InfiniteScalabilityCenter from '../components/scalability/InfiniteScalabilityCenter';
import CosmicWearableHub from '../components/wearables/CosmicWearableHub';
import CosmicGeneticAnalyzer from '../components/genetics/CosmicGeneticAnalyzer';
import UltimateResearchDemo from './UltimateResearchDemo';

interface GraphNode {
  id: string;
  name: string;
  type: 'supplement' | 'ingredient' | 'effect' | 'condition' | 'interaction';
  category?: string;
  size: number;
  color: string;
  metadata?: {
    safetyRating?: number;
    evidenceLevel?: number;
    popularity?: number;
    description?: string;
  };
}

interface GraphLink {
  source: string;
  target: string;
  type: 'contains' | 'causes' | 'interacts' | 'treats' | 'contraindicated';
  strength: number;
  color: string;
  metadata?: {
    evidenceLevel?: number;
    severity?: string;
    mechanism?: string;
  };
}

interface InterfaceTab {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  description: string;
  component: React.ComponentType<any>;
  gradient: string;
  cosmicPower: number;
}

const UltimateHealthInterface: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [cosmicEnergy, setCosmicEnergy] = useState(100);
  const [isInitialized, setIsInitialized] = useState(false);
  const [userProfile] = useState({
    name: 'Health Explorer',
    level: 'Cosmic Practitioner',
    achievements: 12,
    healthScore: 87,
  });
  const [graphData, setGraphData] = useState<{ nodes: GraphNode[]; links: GraphLink[] }>({
    nodes: [],
    links: [],
  });

  useEffect(() => {
    initializeCosmicInterface();
    loadGraphData(); // Load graph data on component mount
  }, []);

  const loadGraphData = async () => {
    try {
      // Simulate API call - replace with actual data fetching
      await new Promise(resolve => setTimeout(resolve, 500)); // Shorter delay for faster testing

      // Generate sample cosmic knowledge graph data
      const sampleNodes: GraphNode[] = [
        { id: 'vitamin-d3', name: 'Vitamin D3', type: 'supplement', size: 25, color: '#3b82f6' },
        { id: 'magnesium', name: 'Magnesium', type: 'ingredient', size: 18, color: '#22c55e' },
        { id: 'bone-health', name: 'Bone Health', type: 'effect', size: 18, color: '#f59e0b' },
        { id: 'sleep-quality', name: 'Sleep Quality', type: 'effect', size: 19, color: '#f59e0b' },
        { id: 'anxiety', name: 'Anxiety', type: 'condition', size: 18, color: '#ef4444' },
      ];

      const sampleLinks: GraphLink[] = [
        { source: 'vitamin-d3', target: 'bone-health', type: 'causes', strength: 0.9, color: '#22c55e' },
        { source: 'magnesium', target: 'sleep-quality', type: 'causes', strength: 0.7, color: '#22c55e' },
        { source: 'magnesium', target: 'anxiety', type: 'treats', strength: 0.6, color: '#3b82f6' },
      ];

      setGraphData({ nodes: sampleNodes, links: sampleLinks });
    } catch (error) {
      console.error('Error loading graph data in UltimateHealthInterface:', error);
    }
  };

  // Define all interface tabs with cosmic power levels
  const interfaceTabs: InterfaceTab[] = [
    {
      id: 'dashboard',
      name: 'Cosmic Dashboard',
      icon: SparklesIcon,
      description: 'Your personalized health universe overview',
      component: CosmicDashboard,
      gradient: 'from-primary-500 via-secondary-500 to-warning-500',
      cosmicPower: 95,
    },
    {
      id: 'profile',
      name: 'Health Profile',
      icon: UserIcon,
      description: 'Comprehensive health data management',
      component: HealthProfileManager,
      gradient: 'from-secondary-500 via-primary-500 to-purple-500',
      cosmicPower: 88,
    },
    {
      id: 'search',
      name: 'Supplement Search',
      icon: MagnifyingGlassIcon,
      description: 'Discover perfect supplements with AI',
      component: CosmicSupplementSearch,
      gradient: 'from-warning-500 via-error-500 to-pink-500',
      cosmicPower: 92,
    },
    {
      id: 'stack',
      name: 'Stack Builder',
      icon: BeakerIcon,
      description: 'Build optimized supplement stacks',
      component: CosmicStackBuilder,
      gradient: 'from-purple-500 via-indigo-500 to-blue-500',
      cosmicPower: 90,
    },
    {
      id: 'graph',
      name: 'Knowledge Graph',
      icon: ChartBarIcon,
      description: 'Explore supplement relationships',
      component: CosmicKnowledgeGraph,
      gradient: 'from-indigo-500 via-blue-500 to-cyan-500',
      cosmicPower: 97,
    },
    {
      id: 'oracle',
      name: 'AI Oracle',
      icon: CpuChipIcon,
      description: 'Quantum-powered health predictions',
      component: CosmicAIOracle,
      gradient: 'from-cyan-500 via-teal-500 to-emerald-500',
      cosmicPower: 99,
    },
    {
      id: 'research',
      name: 'Research Engine',
      icon: GlobeAltIcon,
      description: 'Real-time research integration',
      component: ResearchSingularityEngine,
      gradient: 'from-emerald-500 via-green-500 to-lime-500',
      cosmicPower: 96,
    },
    {
      id: 'ultimate-research',
      name: 'Ultimate Research',
      icon: BeakerIcon,
      description: 'Advanced AI research platform',
      component: UltimateResearchDemo,
      gradient: 'from-violet-500 via-purple-500 to-fuchsia-500',
      cosmicPower: 100,
    },
    {
      id: 'security',
      name: 'Quantum Security',
      icon: ShieldCheckIcon,
      description: 'Zero-trust security center',
      component: QuantumSecurityCenter,
      gradient: 'from-lime-500 via-yellow-500 to-orange-500',
      cosmicPower: 98,
    },
    {
      id: 'scalability',
      name: 'Infinite Scale',
      icon: BoltIcon,
      description: 'Cloud-native scalability',
      component: InfiniteScalabilityCenter,
      gradient: 'from-orange-500 via-red-500 to-pink-500',
      cosmicPower: 100,
    },
    {
      id: 'wearables',
      name: 'Wearable Hub',
      icon: DevicePhoneMobileIcon,
      description: 'Real-time biometric integration',
      component: CosmicWearableHub,
      gradient: 'from-pink-500 via-purple-500 to-indigo-500',
      cosmicPower: 94,
    },
    {
      id: 'genetics',
      name: 'Genetic Analyzer',
      icon: BeakerIcon,
      description: 'Pharmacogenomic analysis',
      component: CosmicGeneticAnalyzer,
      gradient: 'from-indigo-500 via-blue-500 to-cyan-500',
      cosmicPower: 98,
    },
  ];

  useEffect(() => {
    initializeCosmicInterface();
  }, []);

  useEffect(() => {
    // Cosmic energy fluctuation
    const energyInterval = setInterval(() => {
      setCosmicEnergy(() => {
        const currentTab = interfaceTabs.find(tab => tab.id === activeTab);
        const targetEnergy = currentTab?.cosmicPower || 85;
        const fluctuation = (Math.random() - 0.5) * 5;
        const newEnergy = targetEnergy + fluctuation;
        return Math.max(80, Math.min(100, newEnergy));
      });
    }, 2000);

    return () => clearInterval(energyInterval);
  }, [activeTab]);

  const initializeCosmicInterface = async () => {
    // Cosmic initialization sequence
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsInitialized(true);
  };

  const renderActiveComponent = () => {
    const activeTabData = interfaceTabs.find(tab => tab.id === activeTab);
    if (!activeTabData) return null;

    const Component = activeTabData.component;
    // Pass specific props based on the component type
    if (activeTabData.id === 'graph') {
      return <Component userId="cosmic-user" data={graphData} />;
    }
    return <Component userId="cosmic-user" />;
  };

  if (!isInitialized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-900 via-secondary-900 to-warning-900 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center text-white"
        >
          <motion.div
            animate={{ 
              rotate: 360,
              scale: [1, 1.2, 1],
            }}
            transition={{ 
              rotate: { duration: 3, repeat: Infinity, ease: 'linear' },
              scale: { duration: 2, repeat: Infinity, ease: 'easeInOut' }
            }}
            className="w-32 h-32 cosmic-gradient rounded-full flex items-center justify-center mx-auto mb-8"
          >
            <SparklesIcon className="w-16 h-16 text-white" />
          </motion.div>
          
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="text-4xl font-bold mb-4"
          >
            🌟 Initializing Cosmic Health Platform
          </motion.h1>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="text-xl mb-8 text-gray-300"
          >
            Preparing your personalized health universe...
          </motion.p>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9 }}
            className="w-96 mx-auto"
          >
            <div className="cosmic-progress bg-white/20">
              <motion.div 
                className="cosmic-progress__bar"
                initial={{ width: 0 }}
                animate={{ width: '100%' }}
                transition={{ duration: 1.5, ease: 'easeInOut' }}
              />
            </div>
          </motion.div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-primary-50 to-secondary-50">
      {/* Cosmic Header */}
      <motion.header
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white/90 backdrop-blur-xl cosmic-shadow-lg border-b sticky top-0 z-50"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            {/* Logo and Cosmic Status */}
            <div className="flex items-center space-x-6">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: 'linear' }}
                className="w-12 h-12 cosmic-gradient rounded-full flex items-center justify-center"
              >
                <SparklesIcon className="w-7 h-7 text-white" />
              </motion.div>
              
              <div>
                <h1 className="text-2xl font-bold cosmic-gradient bg-clip-text text-transparent">
                  Suplementor Ultimate Health Platform
                </h1>
                <div className="flex items-center space-x-4 text-sm">
                  <span className="text-gray-600">
                    Welcome, {userProfile.name} • {userProfile.level}
                  </span>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-secondary-500 rounded-full cosmic-pulse"></div>
                    <span className="text-gray-500">Cosmic Energy: {Math.round(cosmicEnergy)}%</span>
                  </div>
                </div>
              </div>
            </div>

            {/* User Stats */}
            <div className="flex items-center space-x-6">
              <div className="text-right">
                <div className="text-sm text-gray-500">Health Score</div>
                <div className="text-xl font-bold text-secondary-600">{userProfile.healthScore}</div>
              </div>
              
              <div className="text-right">
                <div className="text-sm text-gray-500">Achievements</div>
                <div className="text-xl font-bold text-warning-600">{userProfile.achievements}</div>
              </div>

              <div className="cosmic-health-score" style={{ width: '50px', height: '50px' }}>
                <div className="cosmic-health-score__value" style={{ fontSize: '12px' }}>
                  {Math.round(cosmicEnergy)}
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Cosmic Navigation */}
      <motion.nav
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white/60 backdrop-blur border-b"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-1 py-4 overflow-x-auto">
            {interfaceTabs.map((tab, index) => (
              <motion.button
                key={tab.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                onClick={() => setActiveTab(tab.id)}
                className={`group relative flex items-center space-x-3 px-6 py-4 rounded-xl font-medium transition-all whitespace-nowrap ${
                  activeTab === tab.id
                    ? `bg-gradient-to-r ${tab.gradient} text-white cosmic-shadow-lg transform scale-105`
                    : 'bg-white/50 text-gray-600 hover:bg-white/80 hover:text-gray-900 hover:scale-102'
                }`}
              >
                <tab.icon className="w-5 h-5" />
                <div className="text-left">
                  <div className="font-semibold">{tab.name}</div>
                  <div className="text-xs opacity-75">{tab.description}</div>
                </div>
                
                {/* Cosmic Power Indicator */}
                <div className="flex flex-col items-center">
                  <div className="text-xs opacity-75">Power</div>
                  <div className="text-sm font-bold">{tab.cosmicPower}%</div>
                </div>

                {/* Active Indicator */}
                {activeTab === tab.id && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-1 -right-1 w-3 h-3 bg-white rounded-full cosmic-pulse"
                  />
                )}

                {/* Hover Effect */}
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
              </motion.button>
            ))}
          </div>
        </div>
      </motion.nav>

      {/* Main Content Area */}
      <main className="relative min-h-screen">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20, scale: 0.98 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: -20, scale: 0.98 }}
            transition={{ 
              duration: 0.4,
              ease: [0.4, 0, 0.2, 1]
            }}
            className="w-full"
          >
            {renderActiveComponent()}
          </motion.div>
        </AnimatePresence>
      </main>

      {/* Cosmic Status Bar */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="fixed bottom-6 right-6 cosmic-card bg-white/95 backdrop-blur-lg cosmic-shadow-lg"
      >
        <div className="flex items-center space-x-4 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-secondary-500 rounded-full cosmic-pulse"></div>
            <span className="text-gray-600">System Active</span>
          </div>
          
          <div className="text-gray-400">|</div>
          
          <div className="flex items-center space-x-2">
            <BoltIcon className="w-4 h-4 text-warning-500" />
            <span className="text-gray-600">AI Processing</span>
          </div>
          
          <div className="text-gray-400">|</div>
          
          <div className="flex items-center space-x-2">
            <SparklesIcon className="w-4 h-4 text-primary-500" />
            <span className="text-gray-600">Cosmic Mode</span>
          </div>
        </div>
      </motion.div>

      {/* Floating Action Button */}
      <motion.button
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 1 }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        className="fixed bottom-6 left-6 w-14 h-14 cosmic-gradient rounded-full flex items-center justify-center cosmic-shadow-lg"
      >
        <LightBulbIcon className="w-7 h-7 text-white" />
      </motion.button>
    </div>
  );
};



export default UltimateHealthInterface;
