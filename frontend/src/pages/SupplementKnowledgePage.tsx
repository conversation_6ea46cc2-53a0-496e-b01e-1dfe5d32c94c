import React, { useState, useMemo } from 'react';
import Card from '@/components/atoms/Card';
import { <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import Button from '@/components/atoms/Button';
import Badge from '@/components/atoms/Badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import GraphVisualization from '@/components/organisms/GraphVisualization';
import GraphLegend from '@/components/organisms/GraphLegend';
import {
  supplementKnowledgeGraph,
  supplementCategories,
  relationshipTypes
} from '@/data/supplementKnowledgeGraph';
import { GraphData, GraphNode } from '@/types';
import {
  Filter,
  Info,
  Zap,
  Heart,
  Shield,
  Brain,
  Pill,
  Leaf,
  Droplets,
  Activity,
  Bot,
  Workflow,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Sparkles
} from 'lucide-react';

// CrewAI Flows interfaces
interface AgentStatus {
  id: string;
  name: string;
  status: 'idle' | 'working' | 'completed' | 'error';
  currentTask?: string;
  progress: number;
  lastUpdate: Date;
}

interface FlowExecution {
  id: string;
  name: string;
  status: 'planning' | 'executing' | 'completed' | 'error';
  progress: number;
  startTime: Date;
  estimatedCompletion?: Date;
  agents: AgentStatus[];
  currentStep: string;
  totalSteps: number;
  completedSteps: number;
}

interface ResearchRequest {
  supplementName: string;
  researchGoals: string[];
  researchDepth: 'basic' | 'comprehensive' | 'exhaustive';
  includeInteractions: boolean;
  includeClinicalTrials: boolean;
  includeUserReviews: boolean;
}

const SupplementKnowledgePage: React.FC = () => {
  const [selectedCategories, setSelectedCategories] = useState<string[]>(supplementCategories);
  const [selectedRelationshipTypes, setSelectedRelationshipTypes] = useState<string[]>(relationshipTypes);
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [, setHoveredNode] = useState<GraphNode | null>(null);

  // CrewAI Flows state
  const [activeFlows, setActiveFlows] = useState<FlowExecution[]>([]);
  const [researchRequest, setResearchRequest] = useState<ResearchRequest>({
    supplementName: '',
    researchGoals: [],
    researchDepth: 'comprehensive',
    includeInteractions: true,
    includeClinicalTrials: true,
    includeUserReviews: false
  });
  const [isResearchActive, setIsResearchActive] = useState(false);
  const [researchResults, setResearchResults] = useState<any[]>([]);

  // Filter data based on selected categories and relationship types
  const filteredData = useMemo((): GraphData => {
    const filteredNodes = supplementKnowledgeGraph.nodes.filter(node => 
      selectedCategories.includes(node.properties.category || 'Unknown')
    );
    
    const nodeIds = new Set(filteredNodes.map(node => node.id));
    
    const filteredRelationships = supplementKnowledgeGraph.relationships.filter(rel => 
      selectedRelationshipTypes.includes(rel.type) &&
      nodeIds.has(typeof rel.source === 'string' ? rel.source : rel.source.id) &&
      nodeIds.has(typeof rel.target === 'string' ? rel.target : rel.target.id)
    );

    return {
      nodes: filteredNodes,
      relationships: filteredRelationships
    };
  }, [selectedCategories, selectedRelationshipTypes]);

  const toggleCategory = (category: string) => {
    setSelectedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const toggleRelationshipType = (type: string) => {
    setSelectedRelationshipTypes(prev => 
      prev.includes(type) 
        ? prev.filter(t => t !== type)
        : [...prev, type]
    );
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Vitamins': return <Pill className="w-4 h-4" />;
      case 'Minerals': return <Droplets className="w-4 h-4" />;
      case 'Omega Fatty Acids': return <Heart className="w-4 h-4" />;
      case 'Probiotics': return <Shield className="w-4 h-4" />;
      case 'Amino Acids': return <Zap className="w-4 h-4" />;
      case 'Herbal Supplements': return <Leaf className="w-4 h-4" />;
      case 'Adaptogens': return <Brain className="w-4 h-4" />;
      case 'Health Areas': return <Activity className="w-4 h-4" />;
      case 'Effects': return <Info className="w-4 h-4" />;
      default: return <Pill className="w-4 h-4" />;
    }
  };

  const getRelationshipColor = (type: string) => {
    const colors = {
      'SUPPORTS': 'bg-green-100 text-green-800',
      'PRODUCES': 'bg-green-200 text-green-900',
      'ENHANCES_ABSORPTION': 'bg-emerald-100 text-emerald-800',
      'SYNERGISTIC_WITH': 'bg-emerald-200 text-emerald-900',
      'REQUIRES': 'bg-blue-100 text-blue-800',
      'COMPETES_WITH': 'bg-yellow-100 text-yellow-800',
      'INTERACTS_WITH': 'bg-red-100 text-red-800',
      'CONTRAINDICATED_WITH': 'bg-red-200 text-red-900',
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const handleNodeClick = (node: GraphNode) => {
    setSelectedNode(node);
  };

  const handleNodeHover = (node: GraphNode | null) => {
    setHoveredNode(node);
  };

  // CrewAI Flows functions
  const startAutonomousResearch = async () => {
    if (!researchRequest.supplementName.trim()) {
      alert('Please enter a supplement name');
      return;
    }

    setIsResearchActive(true);

    try {
      // Simulate CrewAI Flow execution
      const mockFlow: FlowExecution = {
        id: `flow-${Date.now()}`,
        name: `Research: ${researchRequest.supplementName}`,
        status: 'executing',
        progress: 0,
        startTime: new Date(),
        estimatedCompletion: new Date(Date.now() + 300000), // 5 minutes
        agents: [
          {
            id: 'literature_reviewer',
            name: 'Literature Review Specialist',
            status: 'working',
            currentTask: 'Searching PubMed for recent studies',
            progress: 25,
            lastUpdate: new Date()
          },
          {
            id: 'clinical_analyst',
            name: 'Clinical Trial Analyst',
            status: 'working',
            currentTask: 'Analyzing clinical trial data',
            progress: 15,
            lastUpdate: new Date()
          },
          {
            id: 'interaction_mapper',
            name: 'Interaction Mapping Expert',
            status: 'idle',
            progress: 0,
            lastUpdate: new Date()
          },
          {
            id: 'safety_assessor',
            name: 'Safety Assessment Specialist',
            status: 'idle',
            progress: 0,
            lastUpdate: new Date()
          }
        ],
        currentStep: 'Literature Review & Clinical Analysis',
        totalSteps: 5,
        completedSteps: 0
      };

      setActiveFlows([mockFlow]);

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setActiveFlows(flows => flows.map(flow => {
          if (flow.id === mockFlow.id) {
            const newProgress = Math.min(flow.progress + 10, 100);
            const newCompletedSteps = Math.floor((newProgress / 100) * flow.totalSteps);

            return {
              ...flow,
              progress: newProgress,
              completedSteps: newCompletedSteps,
              status: newProgress === 100 ? 'completed' : 'executing',
              agents: flow.agents.map(agent => ({
                ...agent,
                progress: Math.min(agent.progress + Math.random() * 20, 100),
                status: agent.progress >= 100 ? 'completed' : 'working',
                lastUpdate: new Date()
              }))
            };
          }
          return flow;
        }));
      }, 2000);

      // Stop simulation after completion
      setTimeout(() => {
        clearInterval(progressInterval);
        setIsResearchActive(false);
        setResearchResults([
          {
            type: 'Literature Review',
            findings: `Found 47 peer-reviewed studies on ${researchRequest.supplementName}`,
            confidence: 0.92,
            sources: 47
          },
          {
            type: 'Clinical Trials',
            findings: `Identified 12 completed clinical trials with positive outcomes`,
            confidence: 0.88,
            sources: 12
          },
          {
            type: 'Safety Assessment',
            findings: `Generally safe with minor side effects reported in 3% of cases`,
            confidence: 0.85,
            sources: 23
          }
        ]);
      }, 30000);

    } catch (error) {
      console.error('Research failed:', error);
      setIsResearchActive(false);
    }
  };

  const getAgentStatusColor = (status: string) => {
    switch (status) {
      case 'working': return 'text-blue-600 bg-blue-100';
      case 'completed': return 'text-green-600 bg-green-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getFlowStatusIcon = (status: string) => {
    switch (status) {
      case 'executing': return <Clock className="w-4 h-4 text-blue-600" />;
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'error': return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default: return <Bot className="w-4 h-4 text-gray-600" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
            Supplement Knowledge Graph
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Explore the complex relationships between supplements, their benefits, and potential interactions. 
            This interactive visualization helps you understand how different supplements work together.
          </p>
        </div>

        <Tabs defaultValue="visualization" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="visualization">Interactive Graph</TabsTrigger>
            <TabsTrigger value="crewai-research">
              <Bot className="w-4 h-4 mr-2" />
              AI Research
            </TabsTrigger>
            <TabsTrigger value="active-flows">
              <Workflow className="w-4 h-4 mr-2" />
              Active Flows
            </TabsTrigger>
            <TabsTrigger value="legend">Legend</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="relationships">Relationships</TabsTrigger>
          </TabsList>

          <TabsContent value="visualization" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* Filters Sidebar */}
              <Card className="lg:col-span-1">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Filter className="w-5 h-5" />
                    Filters
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Category Filters */}
                  <div>
                    <h4 className="font-semibold mb-2">Categories</h4>
                    <div className="space-y-2">
                      {supplementCategories.map(category => (
                        <Button
                          key={category}
                          variant={selectedCategories.includes(category) ? "primary" : "outline"}
                          size="sm"
                          onClick={() => toggleCategory(category)}
                          className="w-full justify-start text-xs"
                        >
                          {getCategoryIcon(category)}
                          <span className="ml-2 truncate">{category}</span>
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Relationship Type Filters */}
                  <div>
                    <h4 className="font-semibold mb-2">Relationships</h4>
                    <div className="space-y-1">
                      {relationshipTypes.map(type => (
                        <Button
                          key={type}
                          variant={selectedRelationshipTypes.includes(type) ? "primary" : "outline"}
                          size="sm"
                          onClick={() => toggleRelationshipType(type)}
                          className="w-full justify-start text-xs"
                        >
                          <span className="truncate">{type.replace(/_/g, ' ')}</span>
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="pt-4 border-t">
                    <div className="text-sm text-gray-600 space-y-1">
                      <div>Nodes: {filteredData.nodes.length}</div>
                      <div>Relationships: {filteredData.relationships.length}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Main Visualization */}
              <Card className="lg:col-span-3">
                <CardContent className="p-6">
                  <div className="h-[600px] w-full">
                    <GraphVisualization
                      data={filteredData}
                      width={800}
                      height={600}
                      nodeSize={12}
                      linkDistance={100}
                      chargeStrength={-300}
                      onNodeClick={handleNodeClick}
                      onNodeHover={handleNodeHover}
                      className="w-full h-full border rounded-lg"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Node Details Panel */}
            {selectedNode && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {getCategoryIcon(selectedNode.properties.category || '')}
                    {selectedNode.properties.name}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold mb-2">Description</h4>
                      <p className="text-sm text-gray-600 mb-4">
                        {selectedNode.properties.description}
                      </p>
                      
                      <div className="space-y-2">
                        <div>
                          <span className="font-medium">Category: </span>
                          <Badge variant="secondary">{selectedNode.properties.category}</Badge>
                        </div>
                        {selectedNode.properties.targetHealthArea && (
                          <div>
                            <span className="font-medium">Target Health Area: </span>
                            <span className="text-sm">{selectedNode.properties.targetHealthArea}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div>
                      {selectedNode.properties.commonUses && (
                        <div className="mb-3">
                          <h4 className="font-semibold mb-1">Common Uses</h4>
                          <p className="text-sm text-gray-600">{selectedNode.properties.commonUses}</p>
                        </div>
                      )}
                      
                      {selectedNode.properties.dosageRange && (
                        <div className="mb-3">
                          <h4 className="font-semibold mb-1">Dosage Range</h4>
                          <p className="text-sm text-gray-600">{selectedNode.properties.dosageRange}</p>
                        </div>
                      )}
                      
                      {selectedNode.properties.safetyLevel && (
                        <div>
                          <h4 className="font-semibold mb-1">Safety Level</h4>
                          <Badge 
                            variant={selectedNode.properties.safetyLevel === 'Very Safe' ? 'primary' : 'secondary'}
                          >
                            {selectedNode.properties.safetyLevel}
                          </Badge>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="crewai-research" className="space-y-6">
            <div className="max-w-4xl mx-auto space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Sparkles className="w-5 h-5 text-purple-600" />
                    CrewAI Autonomous Research
                  </CardTitle>
                  <p className="text-gray-600">
                    Deploy specialized AI agents to conduct comprehensive supplement research using CrewAI Flows
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Supplement Name</label>
                      <input
                        type="text"
                        value={researchRequest.supplementName}
                        onChange={(e) => setResearchRequest(prev => ({ ...prev, supplementName: e.target.value }))}
                        placeholder="e.g., Vitamin D, Omega-3, Curcumin"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Research Depth</label>
                      <select
                        value={researchRequest.researchDepth}
                        onChange={(e) => setResearchRequest(prev => ({ ...prev, researchDepth: e.target.value as any }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="basic">Basic Research</option>
                        <option value="comprehensive">Comprehensive Analysis</option>
                        <option value="exhaustive">Exhaustive Investigation</option>
                      </select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium">Research Goals</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {[
                        'Safety Profile',
                        'Efficacy Evidence',
                        'Drug Interactions',
                        'Dosage Guidelines',
                        'Clinical Trials',
                        'User Reviews'
                      ].map(goal => (
                        <label key={goal} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={researchRequest.researchGoals.includes(goal)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setResearchRequest(prev => ({
                                  ...prev,
                                  researchGoals: [...prev.researchGoals, goal]
                                }));
                              } else {
                                setResearchRequest(prev => ({
                                  ...prev,
                                  researchGoals: prev.researchGoals.filter(g => g !== goal)
                                }));
                              }
                            }}
                            className="rounded"
                          />
                          <span className="text-sm">{goal}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-4">
                    <div className="text-sm text-gray-600">
                      {isResearchActive ? (
                        <span className="flex items-center gap-2">
                          <Clock className="w-4 h-4 animate-spin" />
                          Research in progress...
                        </span>
                      ) : (
                        'Ready to start autonomous research'
                      )}
                    </div>
                    <Button
                      onClick={startAutonomousResearch}
                      disabled={isResearchActive || !researchRequest.supplementName.trim()}
                      className="flex items-center gap-2"
                    >
                      <Bot className="w-4 h-4" />
                      {isResearchActive ? 'Researching...' : 'Start Research'}
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Research Results */}
              {researchResults.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="w-5 h-5 text-green-600" />
                      Research Results
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {researchResults.map((result, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-semibold">{result.type}</h4>
                            <Badge variant="secondary">
                              Confidence: {(result.confidence * 100).toFixed(0)}%
                            </Badge>
                          </div>
                          <p className="text-gray-600 mb-2">{result.findings}</p>
                          <div className="text-sm text-gray-500">
                            Sources analyzed: {result.sources}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          <TabsContent value="active-flows" className="space-y-6">
            <div className="max-w-6xl mx-auto space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Workflow className="w-5 h-5 text-blue-600" />
                    Active CrewAI Flows
                  </CardTitle>
                  <p className="text-gray-600">
                    Monitor real-time execution of autonomous research workflows
                  </p>
                </CardHeader>
                <CardContent>
                  {activeFlows.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Bot className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p>No active research flows</p>
                      <p className="text-sm">Start a research task to see agent activity here</p>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      {activeFlows.map(flow => (
                        <div key={flow.id} className="border rounded-lg p-6">
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-3">
                              {getFlowStatusIcon(flow.status)}
                              <div>
                                <h3 className="font-semibold">{flow.name}</h3>
                                <p className="text-sm text-gray-600">
                                  Step {flow.completedSteps + 1} of {flow.totalSteps}: {flow.currentStep}
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-2xl font-bold text-blue-600">{flow.progress}%</div>
                              <div className="text-xs text-gray-500">
                                {flow.estimatedCompletion && (
                                  `ETA: ${flow.estimatedCompletion.toLocaleTimeString()}`
                                )}
                              </div>
                            </div>
                          </div>

                          <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${flow.progress}%` }}
                            />
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            {flow.agents.map(agent => (
                              <div key={agent.id} className="border rounded-lg p-3">
                                <div className="flex items-center justify-between mb-2">
                                  <h4 className="font-medium text-sm">{agent.name}</h4>
                                  <Badge className={getAgentStatusColor(agent.status)}>
                                    {agent.status}
                                  </Badge>
                                </div>
                                {agent.currentTask && (
                                  <p className="text-xs text-gray-600 mb-2">{agent.currentTask}</p>
                                )}
                                <div className="w-full bg-gray-200 rounded-full h-1">
                                  <div
                                    className="bg-green-500 h-1 rounded-full transition-all duration-300"
                                    style={{ width: `${agent.progress}%` }}
                                  />
                                </div>
                                <div className="text-xs text-gray-500 mt-1">
                                  {agent.progress.toFixed(0)}% complete
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="legend">
            <div className="max-w-4xl mx-auto">
              <GraphLegend />
            </div>
          </TabsContent>

          <TabsContent value="categories">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {supplementCategories.map(category => {
                const categoryNodes = supplementKnowledgeGraph.nodes.filter(
                  node => node.properties.category === category
                );
                
                return (
                  <Card key={category}>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        {getCategoryIcon(category)}
                        {category}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 mb-3">
                        {categoryNodes.length} supplements in this category
                      </p>
                      <div className="space-y-2">
                        {categoryNodes.slice(0, 3).map(node => (
                          <div key={node.id} className="text-sm">
                            <span className="font-medium">{node.properties.name}</span>
                          </div>
                        ))}
                        {categoryNodes.length > 3 && (
                          <div className="text-xs text-gray-500">
                            +{categoryNodes.length - 3} more...
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          <TabsContent value="relationships">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {relationshipTypes.map(type => {
                const typeRelationships = supplementKnowledgeGraph.relationships.filter(
                  rel => rel.type === type
                );
                
                return (
                  <Card key={type}>
                    <CardHeader>
                      <CardTitle className="text-lg">
                        {type.replace(/_/g, ' ')}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Badge className={getRelationshipColor(type)}>
                        {typeRelationships.length} relationships
                      </Badge>
                      <div className="mt-3 space-y-2">
                        {typeRelationships.slice(0, 3).map(rel => {
                          const sourceNode = supplementKnowledgeGraph.nodes.find(n => n.id === rel.source);
                          const targetNode = supplementKnowledgeGraph.nodes.find(n => n.id === rel.target);
                          
                          return (
                            <div key={rel.id} className="text-sm">
                              <span className="font-medium">{sourceNode?.properties.name}</span>
                              <span className="text-gray-500 mx-2">→</span>
                              <span className="font-medium">{targetNode?.properties.name}</span>
                            </div>
                          );
                        })}
                        {typeRelationships.length > 3 && (
                          <div className="text-xs text-gray-500">
                            +{typeRelationships.length - 3} more...
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default SupplementKnowledgePage;
