import { useState, useEffect, useCallback } from 'react';
import { enhancedResearchService, ResearchQuery, ResearchResult, ResearchFlow, AgentStatus } from '../services/enhancedResearchService';
import { agenticWS } from '../services/api';

// Enhanced Research Hook Interface
interface UseEnhancedResearchReturn {
  // Research Flow Management
  activeFlows: ResearchFlow[];
  currentFlow: ResearchFlow | null;
  
  // Research Data
  results: ResearchResult[];
  insights: any[];
  
  // Agent Status
  agents: AgentStatus[];
  systemStatus: any;
  
  // Loading States
  isResearching: boolean;
  isConnecting: boolean;
  isAnalyzing: boolean;
  
  // Error States
  error: string | null;
  connectionError: string | null;
  
  // Functions
  startAutonomousResearch: (query: ResearchQuery) => Promise<string>;
  stopResearch: (flowId: string) => Promise<void>;
  searchSupplements: (query: string, options?: any) => Promise<any>;
  analyzeInteractions: (supplements: string[]) => Promise<any>;
  getPersonalizedInsights: (userId: string, supplements?: string[]) => Promise<any>;
  clearResults: () => void;
  
  // Real-time Functions
  connectWebSocket: () => Promise<void>;
  disconnectWebSocket: () => void;
  subscribeToFlow: (flowId: string) => void;
  unsubscribeFromFlow: (flowId: string) => void;
}

export const useEnhancedResearch = (): UseEnhancedResearchReturn => {
  // State Management
  const [activeFlows, setActiveFlows] = useState<ResearchFlow[]>([]);
  const [currentFlow, setCurrentFlow] = useState<ResearchFlow | null>(null);
  const [results, setResults] = useState<ResearchResult[]>([]);
  const [insights, setInsights] = useState<any[]>([]);
  const [agents, setAgents] = useState<AgentStatus[]>([]);
  const [systemStatus, setSystemStatus] = useState<any>(null);
  
  // Loading States
  const [isResearching, setIsResearching] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  
  // Error States
  const [error, setError] = useState<string | null>(null);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  // Initialize WebSocket connection and event listeners
  useEffect(() => {
    initializeResearchService();
    return () => {
      enhancedResearchService.off('flow_started', handleFlowStarted);
      enhancedResearchService.off('flow_updated', handleFlowUpdated);
      enhancedResearchService.off('flow_completed', handleFlowCompleted);
      enhancedResearchService.off('agent_updated', handleAgentUpdated);
    };
  }, []);

  const initializeResearchService = () => {
    // Subscribe to research service events
    enhancedResearchService.on('flow_started', handleFlowStarted);
    enhancedResearchService.on('flow_updated', handleFlowUpdated);
    enhancedResearchService.on('flow_completed', handleFlowCompleted);
    enhancedResearchService.on('agent_updated', handleAgentUpdated);
    
    // Load existing flows
    setActiveFlows(enhancedResearchService.getActiveFlows());
  };

  // Event Handlers
  const handleFlowStarted = useCallback((flow: ResearchFlow) => {
    setActiveFlows(prev => [...prev, flow]);
    setCurrentFlow(flow);
    setIsResearching(true);
    setError(null);
  }, []);

  const handleFlowUpdated = useCallback((flow: ResearchFlow) => {
    setActiveFlows(prev => prev.map(f => f.id === flow.id ? flow : f));
    if (currentFlow?.id === flow.id) {
      setCurrentFlow(flow);
    }
  }, [currentFlow]);

  const handleFlowCompleted = useCallback((flow: ResearchFlow) => {
    setActiveFlows(prev => prev.map(f => f.id === flow.id ? flow : f));
    if (currentFlow?.id === flow.id) {
      setCurrentFlow(flow);
      setResults(flow.results);
      setIsResearching(false);
    }
  }, [currentFlow]);

  const handleAgentUpdated = useCallback(({ flowId, agent }: { flowId: string, agent: AgentStatus }) => {
    setActiveFlows(prev => prev.map(flow => {
      if (flow.id === flowId) {
        return {
          ...flow,
          agents: flow.agents.map(a => a.id === agent.id ? agent : a)
        };
      }
      return flow;
    }));
    
    if (currentFlow?.id === flowId) {
      setCurrentFlow(prev => prev ? {
        ...prev,
        agents: prev.agents.map(a => a.id === agent.id ? agent : a)
      } : null);
    }
  }, [currentFlow]);

  // Research Functions
  const startAutonomousResearch = useCallback(async (query: ResearchQuery): Promise<string> => {
    setIsResearching(true);
    setError(null);
    
    try {
      const flowId = await enhancedResearchService.startAutonomousResearch(query);
      return flowId;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start research';
      setError(errorMessage);
      setIsResearching(false);
      throw err;
    }
  }, []);

  const stopResearch = useCallback(async (flowId: string): Promise<void> => {
    try {
      await enhancedResearchService.stopFlow(flowId);
      setIsResearching(false);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to stop research';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const searchSupplements = useCallback(async (query: string, options: any = {}): Promise<any> => {
    setIsAnalyzing(true);
    setError(null);
    
    try {
      const results = await enhancedResearchService.searchSupplements(query, options);
      return results;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to search supplements';
      setError(errorMessage);
      throw err;
    } finally {
      setIsAnalyzing(false);
    }
  }, []);

  const analyzeInteractions = useCallback(async (supplements: string[]): Promise<any> => {
    setIsAnalyzing(true);
    setError(null);
    
    try {
      const analysis = await enhancedResearchService.analyzeInteractions(supplements);
      return analysis;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to analyze interactions';
      setError(errorMessage);
      throw err;
    } finally {
      setIsAnalyzing(false);
    }
  }, []);

  const getPersonalizedInsights = useCallback(async (userId: string, supplements?: string[]): Promise<any> => {
    setIsAnalyzing(true);
    setError(null);
    
    try {
      const insights = await enhancedResearchService.getPersonalizedInsights(userId, supplements);
      setInsights(insights);
      return insights;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get personalized insights';
      setError(errorMessage);
      throw err;
    } finally {
      setIsAnalyzing(false);
    }
  }, []);

  const clearResults = useCallback(() => {
    setResults([]);
    setInsights([]);
    setCurrentFlow(null);
    setError(null);
  }, []);

  // WebSocket Functions
  const connectWebSocket = useCallback(async (): Promise<void> => {
    setIsConnecting(true);
    setConnectionError(null);
    
    try {
      await agenticWS.connect();
      setConnectionError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to connect to WebSocket';
      setConnectionError(errorMessage);
      throw err;
    } finally {
      setIsConnecting(false);
    }
  }, []);

  const disconnectWebSocket = useCallback(() => {
    agenticWS.disconnect();
  }, []);

  const subscribeToFlow = useCallback((flowId: string) => {
    agenticWS.send('subscribe_flow', { flowId });
  }, []);

  const unsubscribeFromFlow = useCallback((flowId: string) => {
    agenticWS.send('unsubscribe_flow', { flowId });
  }, []);

  // Auto-connect WebSocket on mount
  useEffect(() => {
    connectWebSocket().catch(console.error);
    return () => disconnectWebSocket();
  }, [connectWebSocket, disconnectWebSocket]);

  return {
    // Research Flow Management
    activeFlows,
    currentFlow,
    
    // Research Data
    results,
    insights,
    
    // Agent Status
    agents,
    systemStatus,
    
    // Loading States
    isResearching,
    isConnecting,
    isAnalyzing,
    
    // Error States
    error,
    connectionError,
    
    // Functions
    startAutonomousResearch,
    stopResearch,
    searchSupplements,
    analyzeInteractions,
    getPersonalizedInsights,
    clearResults,
    
    // Real-time Functions
    connectWebSocket,
    disconnectWebSocket,
    subscribeToFlow,
    unsubscribeFromFlow,
  };
};

export default useEnhancedResearch;
